import request from '@/utils/request'
import { pref } from '@/utils/common';

// 新生报名列表
export const getEnrollList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/page`, data)
// 毕业生审核列表
export const getEnrollListFn = (data, code) => request.post(`${pref + code}/biz/recruitStudent/pageFn`, data)
// 延缓申请入学报名列表
export const getDelayEnrollList = (data, code) => request.post(`${pref + code}/biz/delayQuery/page`, data)
// 转学报名列表
export const getEnrollListZx = (data, code) => request.post(`${pref + code}/biz/recruitStudent/zhuanXuePage`, data)
// 查询报名类别
export const getTypeList = (data, code) => request.post(`${pref + code}/biz/recruitStudent/type`, data)
// 教育局、学校 - 审核通过
export const passAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/passAudit`, data)
// 审核驳回
export const rejectAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/noPass`, data)
// 调剂
export const adjust = (data, code) => request.post(`${pref + code}/biz/recruitStudent/adjust`, data)
// 报到
export const report = (data, code) => request.post(`${pref + code}/biz/recruitStudent/report`, data)
// 公示录取结果 - 查询提示信息
export const publicity = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicity`, data)
// 公示录取结果 - 确认
export const publicAdmissionResults = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicAdmissionResults`, data)
// 公示录取结果 - 查询提示信息 - 海港转学专用
export const publicityHaiGang = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicityByZhuanXue`, data)
// 公示录取结果 - 确认 - 海港转学专用
export const publicAdmissionResultsHaiGang = (data, code) => request.post(`${pref + code}/biz/recruitStudent/publicAdmissionResultsByZhuanXue`, data)
// 毕业小学发送录取通知书
export const sendRequisitions = (data, code) => request.post(`${pref + code}/biz/enrollQuery/sendRequisitions`, data)

// 发送录取通知书
export const sendRequisition = (data, code) => request.post(`${pref + code}/biz/enrollQuery/sendRequisition`, data)
// 设置优抚类型
export const updateEntitledGroup = (data, code) => request.post(`${pref + code}/biz/recruitStudent/updateEntitledGroup`, data)
// 教育局 - 批量审核通过
export const batchAudit = (data, code) => request.post(`${pref + code}/biz/recruitStudent/batchAudit`, data)

// 批量审核通过 - 海港
export const batchAuditHaiGang = (data, code) => request.post(`${pref + code}/biz/recruitStudent/batchAuditByZhuanXue`, data)

// 公安房管部门 - 审核通过
export const passSecurityAndHouseAudit = (data, code) => request.post(`${pref + code}/biz/security/passSecurityAndHouseAudit`, data)
// 公安部门 - 审核不通过
export const notPassSecurityAndHouseAudit = (data, code) => request.post(`${pref + code}/biz/security/notPassSecurityAndHouseAudit`, data)
// 报名详情
export const adFormDetail = (data, code) => request.post(`${pref + code}/biz/enrollment/getStuEnrollInfo`, data)
// 报名详情 - 审核情况（废弃）
export const getAuditStatus = (data, code) => request.post(`${pref + code}/biz/recruitStudent/auditStatus`, data)
// 报名详情 - 审核情况
export const getAuditRecord = (data, code) => request.post(`${pref + code}/biz/studentLog/getStudentReviewLog`, data)

// 获取转学信息
export const getTransfer = (data, code) => request.post(`${pref + code}/biz/transfer/getTransfer`, data)

/*** 邱县 ***/
// 列表
export const getEnrollListQiuXian = (data, code) => request.post(`${pref + code}/biz/recruitStudent/getVillagesJuniorPage`, data)
// 公示录取结果 - 查询提示信息
export const villagesJuniorPublicity = (data, code) => request.post(`${pref + code}/biz/recruitStudent/villagesJuniorPublicity`, data)

/*** 峰峰矿区 ***/
// 随迁子女列表
// export const migrantChildrenPage = (data, code) => request.post(`${pref + code}/biz/recruitStudent/migrantChildrenPage`, data)
// 随迁子女列表 - 分配学校
// export const migrantChildrenAssignedSchool = (data, code) => request.post(`${pref + code}/biz/recruitStudent/migrantChildrenAssignedSchool`, data)
// 随迁子女列表 - 发送现场验证通知书
// export const fieldValidation = (data, code) => request.post(`${pref + code}/biz/recruitStudent/fieldValidation`, data)
// 随迁子女列表 - 学校驳回教育局的分配
// export const schoolBackDistribution = (data, code) => request.post(`${pref + code}/biz/recruitStudent/schoolBackDistribution`, data)
// 特殊群体列表
// export const specialPage = (data, code) => request.post(`${pref + code}/biz/recruitStudent/specialPage`, data)


// 获取报名入口1
export const getSetupSaveIds = (data, code) => request.post(`${pref + code}/biz/enrollment/getSetupSaveIds`, data)

// 获取报名入口2
export const getSetupSaveDetail = (data, code) => request.post(`${pref + code}/biz/enrollment/getSetupSaveDetail`, data)

// 获取报名入口3
export const getEntryBySchool = (data, code) => request.post(`${pref + code}/biz/enrollment/getEnrollRoute`, data)

// 检查报名入口是否报名时间内1
export const isInAdTimeRange1 = (code) => request.post(`${pref + code}/biz/registrationTime/homePageVerify`)

// 检查报名入口是否报名时间内2
export const isInAdTimeRange2 = (data, code) => request.post(`${pref + code}/biz/registrationTime/getVerifyBySetUpId`, data)

// 查询报名表单
export const qryAdFormByEntry = (data, code) => request.post(`${pref + code}/biz/enrollment/getEnrollFieldConfig`, data)

// 添加报名
export const addAd = (data, code) => request.post(`${pref + code}/biz/enrollment/organizationSubmitEnrollInfo`, data)
// 民办添加报名
export const privateAddAd = (data, code) => request.post(`${pref + code}/privatee/biz/service/organizationSubmitEnrollInfo`, data)
// 区县教育局 - 删除报名
export const deleteEnroll = (data, code) => request.post(`${pref + code}/biz/enrollment/deleteEnrollInfo`, data)
// 区县教育局 - 撤销公示结果
export const revokePublic = (data, code) => request.post(`${pref + code}/biz/recruitStudent/resetPublicStudent`, data)

/***鸡泽***/
export const daoRuWuXuPaiWei=(data,code)=>request.post(`${pref+code}/biz/jzImportStudent/import`,data)

// 获取审核情况中指定的报名详情
export const getEnrollRecord = (data, code) => request.post(`${ pref + code }/biz/studentLog/getStudentInfo`, data)

//取审核情况中指定的优抚类型报名详情
export const getEnrollEntitledRecord = (data, code) => request.post(`${ pref + code }/biz/studentLog/getStudentEntitledInfo`, data)

export const getStudentMemo = (data, code) => request.post(`${pref + code}/biz/enrollment/getStudentMemo`, data)

export const setStudentMemo = (data, code) => request.post(`${pref + code}/biz/enrollment/setStudentMemo`, data)

//市五区报名时间获取详情
export const isStudentZhiSheng = (data, code) => request.post(`${pref + code}/biz/enrollment/isStudentZhiSheng`, data)

// 学校列表
export const deptPageList = params => request.post('/user-api/center/dept/pageList', params)
export const getEnrollListS = (data, code) => request.post(`${pref + code}/biz/recruitStudent/pageS`, data)

// 导入学生录取报名信息
export const importStudentReviewStatus = (data, code) => request.post(`${pref + code}/biz/enrollment/importStudentReviewStatus`, data)
