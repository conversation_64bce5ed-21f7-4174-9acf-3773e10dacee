import request from '@/utils/request';

// 登录 - 账号密码
export const login = (data) => request.post('/sso-center-api/account/login', data)
// 登录 - 获取微信二维码配置
export const sysStateApi = () => request.get('/sso-center-api/account/auth/wxConfig')
// 登录 - 绑定微信
export const wechatBind = (data) => request.get(`/sso-center-api/account/auth/userBanding?code=${data.code}&state=${data.state}`)
// 登录 - 微信二次确认
export const loginConfrimApi = (data) => request.get(`/sso-center-api/account/auth/loginConfirm?code=${data.code}&state=${data.state}`);
// 登录 - 修改密码
export const changePassword = (data) => request.post('/user-api/center/user/changePassword', data)
// 退出登录
export const logout = () => request.post('/sso-center-api/account/logout')
// 获取图形验证码
export const getVerifyCodeImage = () => request.get('/sso-center-api/account/getVerifyCodeImage')

// 忘记密码 ?
export const forgetPwd = (params) => post('/shida-user-center/SmsYzm/forgetPwd', params)
// 获取验证码 ?
export const getSmsCheckCode = (params) => post('/shida-user-center/SmsYzm/getYzmIgnore', params)
//解绑微信 ?
export const unBindWechatXXX = (userId) => request.get('/api/UserOauth/unBanding', userId)

// 验证码
export const getSms = params => request.post('/sso-center-api/account/getSmsYzm', params)