import request from '@/utils/request'

/**
 * 获取学校二维码列表
 * @param {Number} schoolId 学校ID
 * @returns {Promise}
 */
export const getSchoolQrcodeList = (data) => request.post(`/user-api/center/qrcode/list`, data)

/**
 * 添加单个学校二维码
 * @param {Object} data 学校二维码信息
 * @returns {Promise}
 */
export const addSchoolQrcode = (data) => request.post(`/user-api/center/qrcode/addQrcode`, data)

/**
 * 批量添加学校二维码
 * @param {Object} data 包含schoolId和imageUrls的对象
 * @returns {Promise}
 */
export const batchAddSchoolQrcode = (data) => request.post(`/user-api/center/qrcode/batchAddQrcode`, data)

/**
 * 删除学校二维码
 * @param {Number} id 学校二维码ID
 * @returns {Promise}
 */
export const deleteSchoolQrcode = (data) => request.post(`/user-api/center/qrcode/delete`, data) 