import request from '@/utils/request';
import { pref } from '@/utils/common';

// 获取报名系统列表              
export const getSignConfigList = (data, code) => request.post(`${pref + code}/biz/stuPhase/page`, data)
// 新增报名系统
export const signSystemCreate = (data, code) => request.post(`${pref + code}/biz/stuPhase/create`, data)
// 修改报名系统
export const signSystemUpdate = (data, code) => request.post(`${pref + code}/biz/stuPhase/edit`, data)
// 查询报名系统详情
export const signSystemInfo = (data, code) => request.post(`${pref + code}/biz/stuPhase/info`, data)
// 获取报名系统配置树节点
export const getSystemSetUpTree = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/getSystemSetUpTree`, data)
// 报名系统配置树节点 - 提交
export const systemSetupSave = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/saveSystemSetUp`, data)
// 报名系统配置树回显节点
export const getSystemSetupNodes = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/echoSystemSetUpTree`, data)

// 获取功能大纲
export const getSystemSetupTreeFunction = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/functionalOutlineTree`, data)
// 查询主模块字段配置
export const getRootConfigIsShow = (data, code) => request.post(`${pref + code}/biz/fieldConfig/getRootConfigIsShow`, data)
// 设置主模块显示/不显示
export const setRootConfigIsShow = (data, code) => request.post(`${pref + code}/biz/fieldConfig/setRootConfigIsShow`, data)

// 获取配置字段信息
export const getFieldConfigInfo = (data, code) => request.post(`${pref + code}/biz/fieldConfig/getFieldConfigInfo`, data)
// 修改字段中文名称
export const setFieldName = (data, code) => request.post(`${pref + code}/biz/fieldConfig/setFieldName`, data)
// 配置字段信息 - 提交
export const setFieldConfigInfo = (data, code) => request.post(`${pref + code}/biz/fieldConfig/setFieldConfigInfo`, data)

// 区县报名入口 - 状态修改
export const changeOpenStatus = (data) => request.post('/user-api/center/dept/changeOpenStatus', data)
// 区县报名入口 - 提示修改
export const setOpenTips = (data) => request.post('/user-api/center/dept/setOpenTips', data)

// 同步报名类别及字段设置
export const syncEnrollField = (data, code) => request.post(`${pref + code}/biz/fieldConfig/syncEnrollField`, data)