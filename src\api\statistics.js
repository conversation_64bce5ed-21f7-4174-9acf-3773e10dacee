import request from '@/utils/request'
import { pref } from '@/utils/common';

// 教育局 - 首页统计
export const homeStatistics = (data, code) => request.post(`${pref + code}/biz/statistics/pageStatistics`, data)
// 教育局 - 涉县首页统计
export const sheXianHomeStatistics = (data, code) => request.post(`${pref + code}/biz/statistic/getIndex`, data)
// 教育局 - 涉县首页统计
export const sheXianHomeDetail = (data, code) => request.post(`${pref + code}/biz/statistic/getSchoolStatistic`, data)
export const getSchoolStatisticDetail= (data, code) => request.post(`${pref + code}/biz/statistic/getSchoolStatisticDetail`, data)
// 教育局 - 数据统计(小学、初中)
export const schoolStatistics = (data, code) => request.post(`${pref + code}/biz/statistics/schoolStatistics`, data)
// 学校 - 数据统计
export const currentSchoolStatistics = (data, code) => request.post(`${pref + code}/biz/statistics/currentSchoolStatistics`, data)
// 教育局、学校 - 数据统计(小学、初中, 获取设置的报名类别)
export const schoolStatisticsColumn = (data, code) => request.post(`${pref + code}/biz/statistics/schoolStatisticsColumn`, data)
//民办数据统计
export const privateTongJi=(data,code)=>request.post(`${pref + code}/biz/studentPrivateeStatistics/list`,data)
