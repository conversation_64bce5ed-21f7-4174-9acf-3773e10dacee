import request from '@/utils/request';
import { pref } from '@/utils/common';

// 获取清除学生列表
export const getClearStudentList = (data, code) => request.post(`${pref + code}/biz/enrollment/getClearStudentList`, data)
// 获取已报名学生信息
export const getEnrollStudentInfo = (data, code) => request.post(`${pref + code}/biz/enrollment/getEnrollStudentInfo`, data)
// 清除学生报名
export const deleteEnrollInfo = (data, code) => request.post(`${pref + code}/biz/enrollment/deleteEnrollInfo`, data)
// 民办清除学生报名
export const privateDeleteEnrollInfo = (data, code) => request.post(`${pref + code}/privatee/biz/enrollment/deleteEnrollInfo`, data)
// 恢复清除的学生报名信息
export const recoverEnrollInfo = (data, code) => request.post(`${pref + code}/biz/enrollment/recoverEnrollInfo`, data)
// 民办恢复清除的学生报名信息
export const privateRecoverEnrollInfo = (data, code) => request.post(`${pref + code}/privatee/biz/enrollment/recoverEnrollInfo`, data)
// 获取操作日志
export const getLog = data => request.post('/user-api/log/mng/list', data,{timeout:30000})

// 批量清除学生报名数据
export const clearStuBatch = (data, code) => request.post(`${pref + code}/biz/enrollment/importBatchClearStuExcel`, data)
// 民办批量清除学生报名数据
export const privateClearStuBatch = (data, code) => request.post(`${pref + code}/privatee/biz/enrollment/importBatchClearStuExcel`, data)
// 获取清除报名列表
export const deleteStudentPageList = data => request.post('/user-api/center/deleteStudent/pageList', data)

// 获取清除学生详情
export const getStudentInfo = data => request.post('/user-api/center/deleteStudent/getStudentInfo', data)
// 获取清除民办学生详情
export const getPrivateStudentInfo = data => request.post('/user-api/center/deleteStudent/getPrivateeStudentInfo', data)
// 获取转学报名开关设置
export const getTransferSwitch = (code) => request.get(`${pref + code}/biz/enrollment/getTransferSetting`)

// 修改转学报名开关设置
export const setTransferSwitch = (data, code) => request.post(`${pref + code}/biz/enrollment/updateTransferSetting`, data)
