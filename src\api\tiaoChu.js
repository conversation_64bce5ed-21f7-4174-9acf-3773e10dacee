import request from '@/utils/request';
import {pref} from "@/utils/common";
// 上传图片
export const uploadImage = (data) => request.post(`/user-api/user/file/uploadOther`, data)
//跨区分页数据
export const getKuaQuList=(data)=>request.post(`/user-api/center/deptStudent/pageList`,data)
//跨区记录
export const kuaQuJiLu=(data)=>request.post(`/user-api/center/deptStudent/detailList`,data)
//获取跨区补充材料
export const getKuaQuBuChong=(data)=>request.post(`/user-api/center/deptStudent/getStudentMaterial`,data)
//提交跨区补充材料
export const postKuaQuBuChong=(data)=>request.post(`/user-api/center/deptStudent/saveStudentMaterial`,data)
//发送录取通知单
export const sendLuQu=(data)=>request.post(`/user-api/center/deptStudent/sendStudentSchoolContent`,data)
//公示录取结果
export const gongShi=(data)=>request.post(`/user-api/center/deptStudent/publicStudent`,data)
//报到
export  const baoDao1=(data)=>request.post(`/user-api/center/deptStudent/signStudentSchool`,data)
//区县调剂学校
export const quXianTiaoJiao=(data)=>request.post(`/user-api/center/deptStudent/saveStudentSchool`,data)
//调出
export const shiTiaoChu=(data,code)=>request.post(`${pref + code}/five/spanStudent/addSpanStudent`,data)
//标准版调出
export const biaoZhunTiaoChu=(data,code)=>request.post(`${pref + code}/biz/cityAdjustment/adjustment`,data)
