<template>
	<div class="enroll-detail">
		<el-skeleton
			:loading="loadingAdForm"
			animated
		>
			<template slot="default">
				<el-alert
          v-if="adSchool"
					style="margin-bottom: 10px;"
					type="success"
					effect="dark"
					:title="`报名学校：${ adSchool }`"
					:closable="false">
				</el-alert>
					<!-- 普通字段 -->
					<dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
						<dt class="f-group-title">{{ item.infoName }}</dt>
						<dd class="f-group-detail">
							<div>
								<ul class="f-g-d-info-list">
									<li v-for="fi, fidx in item._normalItem" :key="fidx" class="f-g-d-item normal-item">
										<div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
										<div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
										<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
											<normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
										</div>
									</li>
								</ul>
							</div>
							<div>
								<!-- <ul class="f-g-d-info-list">
									<li v-for="fi, fidx in item._imgItem" :key="fidx" class="f-g-d-item img-item">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</li>
								</ul> -->
                <viewer :images="switchImgList(item._imgItem)" >
                  <ul class="f-g-d-info-list">
                    <li v-for="(fi, fidx) in switchImgList(item._imgItem)" :key="fidx" class="f-g-d-item img-item">
                      <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px" />
                      <div :class="{ 'is-modify': fi.isModify == 1 }">{{fi.fieldName}}</div>
                    </li>
                  </ul>
                </viewer>
							</div>
						</dd>
					</dl>
					<!-- 双胞胎 -->
					<dl class="form-group" v-if="siblings.list.length > 0">
						<dt class="f-group-title">双胞胎信息</dt>
						<dd class="f-group-detail">
							<ul class="sib-list">
								<li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
									 <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
									<div>
										<ul class="f-g-d-info-list">
											<li v-for="fi, fidx in si._normalItem" :key="fidx" class="f-g-d-item normal-item">
												<div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
												<div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
												<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
													<normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
												</div>
											</li>
										</ul>
									</div>
									<div>
										<!-- <ul class="f-g-d-info-list">
											<li v-for="fi, fidx in si._imgItem" :key="fidx" class="f-g-d-item img-item">
												<normal-img-ex :item-config.sync="fi"></normal-img-ex>
											</li>
										</ul> -->
                    <viewer :images="switchImgList(si._imgItem)" >
                      <ul class="f-g-d-info-list">
                        <li v-for="(fi, fidx) in switchImgList(si._imgItem)" :key="fidx" class="f-g-d-item img-item">
                          <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px" />
                          <div :class="{ 'is-modify': fi.isModify == 1 }">{{fi.fieldName}}</div>
                        </li>
                      </ul>
                    </viewer>
									</div>
								</li>
							</ul>
						</dd>
					</dl>
					<!-- 房产 -->
					<template v-if="propertyForm.list.length > 0">
						<dl v-for="ppItem, ppIdx in propertyForm.list" :key="ppItem.typeConfigId" class="form-group">
							<dt class="f-group-title f-group-title-pp">
								<span>房产信息</span>
								<span>{{ propertyForm.list[0].infoName }}</span>
							</dt>
							<dd class="f-group-detail">
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in ppItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
											<div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
											<div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
											<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
												<normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
											</div>
										</li>
									</ul>
								</div>
								<div>
									<!-- <ul class="f-g-d-info-list">
										<li v-for="fi, fidx in ppItem._imgItem" :key="fidx" class="f-g-d-item img-item">
											<normal-img-ex :item-config.sync="fi"></normal-img-ex>
										</li>
									</ul> -->
                  <viewer :images="switchImgList(ppItem._imgItem)" >
                    <ul class="f-g-d-info-list">
                      <li v-for="(fi, fidx) in switchImgList(ppItem._imgItem)" :key="fidx" class="f-g-d-item img-item">
                        <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px" />
                        <div :class="{ 'is-modify': fi.isModify == 1 }">{{fi.fieldName}}</div>
                      </li>
                    </ul>
                  </viewer>
								</div>
							</dd>
						</dl>
					</template>
					<!-- 其它 -->
					<template v-if="others.list.length > 0">
						<dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
              <dt class="f-group-title" v-if="prefixDeptCode == '130481'">乡镇购房材料</dt><!-- 武安 -->
							<dt class="f-group-title" v-else>其他材料证明</dt>
							<dd class="f-group-detail">
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in oItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
											<div class="desc-txt" :class="{ 'is-modify': fi.isModify == 1 }">{{ fi.fieldName }}：</div>
											<div class="res-txt" :class="{ 'is-modify': fi.isModify == 1 }" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
											<div class="res-txt"  v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
												<normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
											</div>
										</li>
									</ul>
								</div>
								<!-- <ul class="f-g-d-info-list">
									<li v-for="fi, fidx in oItem._imgItem" :key="fidx" class="f-g-d-item img-item">
										<normal-img-ex :item-config.sync="fi"></normal-img-ex>
									</li>
								</ul> -->
                <viewer :images="switchImgList(oItem._imgItem)" >
                  <ul class="f-g-d-info-list">
                    <li v-for="(fi, fidx) in switchImgList(oItem._imgItem)" :key="fidx" class="f-g-d-item img-item">
                      <img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px" />
                      <div :class="{ 'is-modify': fi.isModify == 1 }">{{fi.fieldName}}</div>
                    </li>
                  </ul>
                </viewer>
							</dd>
						</dl>
					</template>
					<!-- 优抚对象 -->
					<template v-if="uf.list.length > 0">
						<dl v-for="ppItem, ppIdx in uf.list" :key="ppItem.typeConfigId" class="form-group">
							<dt class="f-group-title f-group-title-pp">
								<span>优抚信息</span>
								<span>{{ uf.list[0].infoName }}</span>
							</dt>
							<dd class="f-group-detail">
								<div>
									<ul class="f-g-d-info-list">
										<li v-for="fi, fidx in ppItem._normalItem" :key="fidx" class="f-g-d-item normal-item">
											<div class="desc-txt">{{ fi.fieldName }}：</div>
											<div class="res-txt" :item-config.sync="fi" v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4">{{ fi.fieldValue }}</div>
											<div class="res-txt" v-else-if="fi.inputItemCode == 5 || fi.inputItemCode == 6 || fi.inputItemCode == 2">
												<normal-select-ex :item-config.sync="fi" :list-item-config.sync="listItemDetail"></normal-select-ex>
											</div>
										</li>
									</ul>
								</div>
								<div>
									<viewer :images="switchImgList(ppItem._imgItem)">
										<ul class="f-g-d-info-list">
											<li v-for="(fi, fidx) in switchImgList(ppItem._imgItem)" :key="fidx" class="f-g-d-item img-item">
												<img :src="fi._fieldValue" :alt="fi.fieldName" style="width: 148px; height: 148px" />
												<div :class="{ 'is-modify': fi.isModify == 1 }">{{fi.fieldName}}</div>
											</li>
										</ul>
									</viewer>
								</div>
							</dd>
						</dl>
					</template>
          <template>
            <dl class="form-group" v-if="entitledGroupFlag">
              <dt class="f-group-title">优抚类型</dt>
              <dd class="f-group-detail">
                <ul class="f-g-d-info-list entitled-group-list">
                  <li v-for="(item,index) in entitledGroupForm.fields" :key="index" class="f-g-d-item normal-item">
                    <div class="entitled-item" :class="{ 'is-modify': item.isModify == 1 }">
                      <span class="desc-txt" >{{ item.label }}：</span>
                      <span class="res-txt" v-if="item.type !== 'upload'">{{ item.value }}</span>
                      <normal-img-ex v-else :itemConfig="{filedName: item.label, fieldId: item.prop,fieldValue: item.value}" />
                    </div>
                  </li>
                </ul>
              </dd>
            </dl>
          </template>
			</template>
		</el-skeleton>
	</div>
</template>

<script>
import {getEnrollEntitledRecord, getEnrollRecord} from "@/api/enrollment.js"
import NormalSelectEx from "@/components/Exhibition/NormalSelectEx"
import NormalImgEx from "@/components/Exhibition/NormalImgEx"
import { imgPrefix } from '@/utils/common'
export default {
	name: 'enroll-record-detail',
	components: {
		NormalSelectEx,
		NormalImgEx
	},
	data() {
		return {
      prefixDeptCode: this.$store.getters.deptCode,
      imgPrefix: imgPrefix(),
			// 加载中
			loadingAdForm: true,
			// 报名人
			stuId: '',
			// 报名的学校
			adSchool: '',
			// 整个页面循环用的list
			originList: [],
			// 非房产非双胞胎的普通字段
			normalForm: [],
			// 房产
			propertyForm: {
				list: []
			},
			// 多胞胎
			siblings: {
				list: []
			},
			// 其他补充信息
			others: {
				list: []
			},
      //优抚对象
      uf: {
        list: []
      },
			// 报名列表当前行的详情
			listItemDetail: {},
      //优抚类型
      entitledGroupFlag: false,
      entitledGroupForm:{
        fields: []
      },
		}
	},
	props: {
		studentId: {
			required: true
		}
	},
  computed: {
    // viewer转换
    switchImgList() {
      return (list) => {
        let imgList = list
        imgList.forEach(item => {
          item._fieldValue = this.imgPrefix + item.fieldValue
        })
        return imgList
      }
    }
  },
	created() {
		// this.listItemDetail = this.stuDetail
		this.stuId = this.studentId
		// this.adSchool = this.stuDetail.enrollSchoolName
		this.getAdDetail()
    //130202  130271  130285
    // if(this.prefixDeptCode !== '130299'){//非公民同报进入，后续其他不需要优抚对象的一一加入即可。
    //   this.getEnrollEntitledRecord();
    // }
    if(['130202','130271','130285'].includes(this.prefixDeptCode)){
      this.getEnrollEntitledRecord();
    }
	},
	methods: {
		// 获取报名字段
		getAdDetail() {
			getEnrollRecord({
				key: this.stuId
			}, this.$store.getters.deptCode).then(res => {
				// 按typeConfigId从小到大排序
				let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
				
				// 修复优抚信息中关系字段的infoVerificationCode
				resCopy.forEach(item => {
					if (item.typeConfigId >= 20 && item.typeConfigId < 25) {
						item.leafFieldInfos.forEach(field => {
							// 查找与关系相关的字段，通常字段名包含"关系"
							if (field.fieldName && (field.fieldName.includes('关系') || field.fieldName === '与该生关系')) {
								// 如果infoVerificationCode为2或16（错误的代码），修改为10（关系类型）
								if (field.infoVerificationCode === 2 || field.infoVerificationCode === 16) {
									field.infoVerificationCode = 10;
								}
								
								// 修正fieldValue
								if (field.fieldValue === '农业' || field.fieldValue === 1 || field.fieldValue === '1') {
									field.fieldValue = '1'; // 父子
								} else if (field.fieldValue === '非农业' || field.fieldValue === 2 || field.fieldValue === '2') {
									field.fieldValue = '2'; // 父女
								} else if (field.fieldValue === 3 || field.fieldValue === '3') {
									field.fieldValue = '3'; // 母子
								} else if (field.fieldValue === 4 || field.fieldValue === '4') {
									field.fieldValue = '4'; // 母女
								}
							}
						});
					}
				});
				
				// 取基础信息里的学生身份证
				this.stuIdCard = resCopy.find(v => v.typeConfigId == 1).leafFieldInfos.find(v => v.fieldId == 3).fieldValue
				// 分类
				this.originList = resCopy.map(this.separateImgAndNormal)

        // 优抚类型：
        this.uf.list = this.originList.filter(v => v.typeConfigId >= 20 &&  v.typeConfigId < 25)

				// 非房产非双胞胎的普通字段，typeConfigId为1，2，4，5，6，7
				this.normalForm = this.originList.filter(v => v.typeConfigId < 8 && v.typeConfigId != 3)
				// 房产字段：typeConfigId >= 8但小于19
				this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 &&  v.typeConfigId < 18)
				// 双胞胎字段：typeConfigId为3和19
				this.siblings.list = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
				// 其他补充信息
				this.others.list = this.originList.filter(v => v.typeConfigId == 18)
				// 加载完成
				this.loadingAdForm = false
			})
		},
    getEnrollEntitledRecord(){
      getEnrollEntitledRecord({
        key: this.stuId
      }, this.$store.getters.deptCode).then(res => {
        if (res) {
          if(res.options){
            this.entitledGroupForm = {
              fields: JSON.parse(res.options)
            }
            this.entitledGroupFlag = true;
          }
        }
      })
    },
		// 字段通用处理：区分图片与非图片字段
		separateImgAndNormal(item) {
			// 非图片字段
			item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
			// 图片字段
			item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2 && fi.fieldValue)
			return item
		}
	}
}
</script>

<style lang="scss" scoped>
.enroll-detail {
	.common-title {
		margin-bottom: 10px;
	}
}
.form-group {
	margin: 0;
	.f-group-title {
		margin-bottom: 20px;
		padding: 0 15px;
		line-height: 46px;
		border-radius: 5px;
		background-color: #F1F1F1;
	}
	.f-group-title-pp {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.f-group-detail {
		margin: 0;
		margin-bottom: 30px;
		.f-g-d-info-list, .f-g-d-img-list {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;
		}
		.f-g-d-info-list {
			padding: 0 10px;
		}
		.normal-item {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex: 0 0 33.3333%;
			margin-bottom: 10px;
		}
		.img-item {
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			flex: 0 0 14.2857%;
			margin: 15px 0;
			.desc-txt {
				margin-top: 10px;
			}
		}
	}
}
.entitled-group-list {
  // 使内容横向排列（标签在左，内容在右）
  .entitled-item {
    display: flex;
    align-items: center;

    .desc-txt {
      margin-right: 8px;  // 标签和内容之间的间距
      min-width: 80px;    // 固定标签宽度（可选）
    }

    .res-txt {
      flex: 1;            // 内容区域自动填充剩余空间
    }
  }

  // 如果希望保持和其他表单相同的网格布局
  .normal-item {
    flex: 0 0 33.3333%;   // 每行显示3项
    margin-bottom: 10px;
  }
}
</style>
