<template>
		<!-- 报名详情查看：select字段 -->
	<div class="normal-select-ex" v-if="!loading">
		<span :class="{ 'is-modify': isModify }">{{ modelData }}</span>
<!--		<span v-else>加载中...</span>-->
	</div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import {getSchoolRange} from "@/api/setting.js"
import { getDepts } from "@/api/user.js"
import {deptPageList} from "@/api/enrollment"
import areasData from "@/utils/areas.json"
export default {
	name: 'normal-select-ex',
	data() {
		return {
			modelData: '',
			fieldVal: '',
			// stuEnrollNature: '',
			enrollSchoolId: '',
			loading: true // 添加加载状态变量
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		listItemConfig: {
			type: Object,
			required: true
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		},
		// 是否已修改
		isModify() {
			return this.itemConfig.isModify == 1
		}
	},
	created() {
		// console.log()
		// this.stuEnrollNature = this.listItemConfig.nature == '乡镇' ? 1 : 2
		this.enrollSchoolId = this.listItemConfig.enrollSchoolId
		// this.enrollSchoolId = '1798651984557735937'
		this.fieldVal = this.itemConfig.fieldValue
		this.modelData = '' // 初始化为空字符串，避免显示ID
		
		// 日期字段的验证码，根据实际情况可能需要调整
		let dateCodeIdx = [1, 2, 11, 14, 15, 22, 23]
		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
		let dicListIdx = [3, 4, 5, 9, 10, 12, 13, 16, 17, 18, 20, 24, 25]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8, 19, 21]
		
		if (dateCodeIdx.indexOf(this.verCode) !== -1) {
			// 处理日期字段
			this.formatDateField()
			this.loading = false
		} else if (dicListIdx.indexOf(this.verCode) != -1) {
			// 特殊处理：南堡开发区(130285)的"与该生关系"字段使用规则10的关系列表
			let targetList = rulesList[this.verCode].list
			if (this.$store.getters.deptCode === '130285' && this.verCode === 16 && this.itemConfig.fieldName === '与该生关系') {
				targetList = rulesList[10].list
			}

			let matchItem = targetList.filter(v => v.id == this.fieldVal)

			if (matchItem.length > 0) {
				this.modelData = matchItem[0].val
			} else {
				this.modelData = ''
			}
			this.loading = false // 同步处理完成后设置loading为false
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			// 异步请求开始前确保loading为true
			this.loading = true
			if (this.verCode == 6) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			} else if (this.verCode === 19) {
				this.getChildrenStr();
			} else if (this.verCode === 21) {
				this.patchSchoolList()
			} else {
				this.loading = false
			}
		} else {
			// 如果没有匹配的处理逻辑，也需要设置loading为false
			this.loading = false
		}
	},
	methods: {
		// 格式化日期字段
		formatDateField() {
			if (!this.fieldVal) {
				this.modelData = ''
				return
			}
			
			try {
				// 尝试将日期字符串转换为Date对象
				const date = new Date(this.fieldVal)
				if (isNaN(date.getTime())) {
					// 如果转换失败，保留原值
					this.modelData = this.fieldVal
					return
				}
				
				// 格式化日期为 YYYY-MM-DD 格式
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				
				// 如果包含时间部分，则添加时间
				if (this.fieldVal.includes(':')) {
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')
					const seconds = String(date.getSeconds()).padStart(2, '0')
					this.modelData = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
				} else {
					this.modelData = `${year}-${month}-${day}`
				}
			} catch (error) {
				console.error('日期格式化失败:', error)
				this.modelData = this.fieldVal
			}
		},
		
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {

      let  param = {
        keywords: "",
            nature: '',
          // 学段
          period: 2,
          deptCode: this.$store.getters.deptCode,
          type: 1,
          pageNumber: 1,
          pageSize: 9999
      }
      if(this.$store.getters.deptCode ==='130284'){
        //取消单独学校类型查询，公办民办一块查询
        param = {
          keywords: "",
          nature: '',
          period: 2,
          deptCode: this.$store.getters.deptCode,
          pageNumber: 1,
          pageSize: 9999
        }
      }
			getDepts(param).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName

					}
          // 如果是毕业小学字段(fieldId: 600)且值为"999999"，显示"其他"
          if (this.itemConfig.fieldId == 600 && this.fieldVal == '999999') {
            this.modelData = '其他'
          }
				})
				this.loading = false // 请求完成后设置loading为false
			}).catch(() => {
				this.loading = false // 请求失败也需要设置loading为false
			})
		},
    // infoVerificationCode == 21 时，片区学校列表
    patchSchoolList() {
      let params = {
        keywords: "",
        nature: '',
        // 学段：小学
        period: 2,
        deptCode: this.$store.getters.deptCode,
        type: 1,
        pageNumber: 1,
        pageSize: 9999
      };
      getDepts(params).then(res => {
        console.log("进入循环")
        res.forEach(v => {
          if (v.id == this.fieldVal) {
            this.modelData = v.deptName
          }
        })
        this.loading = false // 请求完成后设置loading为false
      }).catch(() => {
        this.loading = false // 请求失败也需要设置loading为false
      });
    },
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			getDepts({
				keywords: "",
				nature: '',
				// 学段
				period: 1,
				deptCode: this.$store.getters.deptCode,
				type: 1,
				pageNumber: 1,
				pageSize: 9999
			}).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.deptName
					}
				})
				this.loading = false // 请求完成后设置loading为false
			}).catch(() => {
				this.loading = false // 请求失败也需要设置loading为false
			})
		},
		// infoVerificationCode == 8时，范围列表
		rangeList() {
			getSchoolRange({
				schoolId: this.enrollSchoolId,
				type: 2
			}).then(res => {
				res.forEach(v => {
					if (v.id == this.fieldVal) {
						this.modelData = v.rangeName
					}
				})
				this.loading = false // 请求完成后设置loading为false
			}).catch(() => {
				this.loading = false // 请求失败也需要设置loading为false
			})
		},
    getChildrenStr() {
      if (this.fieldVal && areasData) {
        // 将存储的值转换为省市区名称
        const regionNames = this.convertValueToRegionNames(this.fieldVal, areasData)
        this.modelData = regionNames
      }
      this.loading = false // 处理完成后设置loading为false
    },
    // 将数值ID转换为省市区名称
    convertValueToRegionNames(value, dataSource) {
      if (!value || !dataSource) return ''

      // 将字符串转换为数组
      let valueArray = []
      if (typeof value === 'string') {
        valueArray = value.split(',').map(v => parseInt(v.trim()))
      } else if (Array.isArray(value)) {
        valueArray = value
      } else {
        return ''
      }

      // 递归查找对应的名称
      const findNames = (options, values, index = 0) => {
        if (index >= values.length || !options) return []

        const currentValue = values[index]
        const currentOption = options.find(opt => opt.value === currentValue)

        if (!currentOption) return []

        if (index === values.length - 1) {
          // 最后一级，返回名称
          return [currentOption.label]
        } else {
          // 继续查找下一级
          const nextNames = findNames(currentOption.children, values, index + 1)
          return [currentOption.label, ...nextNames]
        }
      }

      const names = findNames(dataSource, valueArray)
      return names.join(' / ')  // 输出：北京 / 朝阳区 / 管庄地区
    },
	}
}
</script>
