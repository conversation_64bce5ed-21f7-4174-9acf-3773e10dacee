<template>
	<div class="normal-input">
		<el-input v-model.trim="modelData" :placeholder="phTxt" style="width: 220px;"></el-input>
	</div>
</template>

<script>
export default {
	name: 'normal-input',
	data() {
		return {
			modelData: '',
			phTxt: '',
			fId: ''
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				this.valChange(newV)
			}
		}
	},
	created() {
		this.phTxt = `请输入${ this.itemConfig.fieldName }`
		this.fId = this.itemConfig.fieldId
	},
	methods: {
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		}
	}
}
</script>
