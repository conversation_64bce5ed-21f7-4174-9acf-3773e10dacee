<template>
	<div class="normal-select">
		<!-- 日期选择 -->
		<el-date-picker 
			v-if="verCode == 2 || verCode == 14"
			v-model="modelData"
			type="date"
			value-format="yyyy-MM-dd"
			:placeholder="phTxt"
		></el-date-picker>
    <el-cascader
        v-else-if="verCode == 19"
        v-model="modelData"
        :options="dataSource"
        :props="{
        expandTrigger: 'hover',
        value: 'value',
        label: 'label',
        children: 'children'
        }"
        :placeholder="phTxt"
        clearable
        style="width: 220px;"
        @change="handleCascaderChange"
    ></el-cascader>
		<!-- 其它下拉选择 -->
		<el-select
			v-else
			size="small"
			clearable
			style="width: 220px;"
			v-model="modelData" 
			:placeholder="phTxt"
			:filterable="isFilterable"
		>
			<el-option v-for="item, idx in dataSource" :key="idx" :label="item.val" :value="item.id"></el-option>
		</el-select>
	</div>
</template>

<script>
import { rulesList } from "@/utils/dictionary"
import { getSchoolRange,deptPageList ,xiaoquList} from "@/api/setting.js"
import { getDepts } from "@/api/user.js"
import areasData from "@/utils/areas.json"
export default {
	name: 'normal-select',
	data() {
		return {
			modelData: '',
			phTxt: '',
			dataSource: [],
			isFilterable: false,
			searchSchool: {
				deptCode: '',
				keywords: "",
				nature: "",
				period: "",
				status: "",
				type: 1,
				level: 3,
				pageNumber: 1,
				pageSize: 9999
			},
			schoolId: ''
		}
	},
	props: {
		// 字段所有配置
		itemConfig: {
			type: Object,
			required: true
		},
		// 额外参数
		extraConfig: {
			type: Object,
			required: true
		}
	},
	watch: {
		modelData(newV, oldV) {
			if (newV != oldV) {
				// 对于级联选择器，确保发送的是字符串格式
				if (this.verCode === 19 && Array.isArray(newV)) {
					this.valChange(newV.join(','))
				} else {
					this.valChange(newV)
				}
			}
		}
	},
	computed: {
		// 验证规则的快捷方式
		verCode() {
			return this.itemConfig.infoVerificationCode
		}
	},
	created() {
		this.searchSchool.deptCode = this.extraConfig.deptCode
		// this.searchSchool.nature = this.extraConfig.nature
		this.schoolId = this.extraConfig.schoolId
		this.phTxt = `请选择${ this.itemConfig.fieldName }`
		// inputItemCode为6时开启filterable属性
		this.isFilterable = this.itemConfig.inputItemCode == 6

		// 初始化modelData
		if (this.itemConfig.fieldValue) {
			this.modelData = this.itemConfig.fieldValue
		}

		// infoVerificationCode为3，4，5，9，10，12，13时直接读取字典里的list
		let dicListIdx = [3, 4, 5, 9, 10, 12, 13,16,17,18,20,24,25]
		// infoVerificationCode为6，7，8时需要请求api获取数据
		let qryListIdx = [6, 7, 8,19,21,23]
		if (dicListIdx.indexOf(this.verCode) != -1) {
			// 特殊处理：南堡开发区(130285)的"与该生关系"字段使用规则10的关系列表
			if (this.extraConfig.deptCode === '130285' && this.verCode === 16 && this.itemConfig.fieldName === '与该生关系') {
				this.dataSource = rulesList[10].list
			} else {
				this.dataSource = rulesList[this.verCode].list
			}
		} else if (qryListIdx.indexOf(this.verCode) != -1) {
			if (this.verCode == 6 || this.verCode == 21) {
				this.primarySchoolList()
			} else if (this.verCode == 7) {
				this.preSchoolList()
			} else if (this.verCode == 8) {
				this.rangeList()
			}else if (this.verCode === 19) {
        this.regionList()
      }else if (this.verCode === 21) {
        this.pageList()
      }else if(this.verCode == 23) {
        this.xiaoquList()
      }
		}
	},
	methods: {
		// emit
		valChange(data) {
			this.modelData = data
			this.$emit('value-change', {
				id: this.itemConfig.fieldId,
				val: data
			})
		},
    // infoVerificationCode == 23时，小区列表
    xiaoquList() {
      let params = {
        key:this.$store.state.schoolDetail.id,
      }
      xiaoquList(params).then(res => {
        // API返回的数据直接是数组格式
        let dataList = res || []
        dataList.forEach(v => {
          v.val = v.rangeName
        })
        this.dataSource = dataList
      })
    },
    pageList(){
      let params ={
        keywords: "",
        nature: '', // 使用存储的区域ID作为nature参数
        // 学段
        period: 2,
        type: this.$store.state.entry4,
        deptCode: this.$store.state.deptCode,
        pageNumber: 1,
        pageSize: 9999
      }
      deptPageList(params).then(res => {
        res.records.forEach(v => {
          this.dataSource = v.records
        })
        this.dataSource = res.records
      })
    },
    handleCascaderChange(value) {
      if (value && value.length > 0) {
        // 数组转为字符串
        const strValue = value.join(',');
        // 发送数据变更
        this.$emit('value-change', {
          id: this.itemConfig.fieldId,
          val: strValue
        });
        // 特殊处理意向地字段 - 表单验证
        if (this.verCode === 19) {
          let parent = this.$parent;
          while (parent && !parent.$refs.form) {
            parent = parent.$parent;
          }
          if (parent && parent.$refs.form) {
            this.$nextTick(() => {
              parent.$refs.form.clearValidate(this.itemConfig.fieldId);
            });
          }
        }
      }
    },
		// infoVerificationCode == 6时，小学列表
		primarySchoolList() {
			let params = { ...this.searchSchool }
			params.period = 2
			getDepts(params).then(res => {
				res.forEach(v => {
					v.val = v.deptName
				})

				this.dataSource = res
        if (this.itemConfig.fieldId == 600 && this.$store.state.deptCode === '130285') {
          this.dataSource.push({
            id: '999999',
            val: '其他',
            deptName: '其他'
          })
        }
			})

		},
    // 获取省市区数据
    regionList() {
      // 直接使用本地JSON数据，不需要API调用
      this.dataSource = areasData;
      if (this.itemConfig.fieldValue && this.verCode === 19) {
        // 将已有的值转换为数组形式用于显示
        this.modelData = this.itemConfig.fieldValue.split(',').map(Number);
        // 确保fieldValue保持字符串格式
        this.$emit('value-change', {
          id: this.itemConfig.fieldId,
          val: this.itemConfig.fieldValue
        });
      }
    },
		// infoVerificationCode == 7时，幼儿园列表
		preSchoolList() {
			let params = { ...this.searchSchool }
			params.period = 1
			getDepts(params).then(res => {
				res.forEach(v => {
					v.val = v.deptName
				})
				this.dataSource = res
			})
		},
		// infoVerificationCode == 8时，范围列表
    rangeList() {
      getSchoolRange({
        schoolId: this.schoolId,
        type: 2
      }).then(res => {
        res.forEach(v => {
          v.val = v.rangeName
        })
        this.dataSource = res
      })
    },
	}
}
</script>
