<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.enrollId"
              placeholder="报名ID"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.studentName"
              placeholder="姓名或身份证"
              clearable
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-date-picker
              size="small"
              v-model="search.houseDate"
              type="date"
              placeholder="购房合同时间"
              clearable
            ></el-date-picker>
          </el-form-item> -->
<!--          <el-form-item>-->
<!--            <el-date-picker-->
<!--              size="small"-->
<!--              v-model="search.createdTime"-->
<!--              type="datetime"-->
<!--              placeholder="报名开始时间"-->
<!--              value-format="yyyy-MM-dd HH:mm:ss"-->
<!--              clearable-->
<!--            ></el-date-picker>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-date-picker-->
<!--              size="small"-->
<!--              v-model="search.endTime"-->
<!--              type="datetime"-->
<!--              placeholder="报名结束时间"-->
<!--              value-format="yyyy-MM-dd HH:mm:ss"-->
<!--              clearable-->
<!--            ></el-date-picker>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.enrollSchoolId"-->
<!--              placeholder="报名学校"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option-->
<!--                v-for="item in schoolList"-->
<!--                :label="item.deptName"-->
<!--                :value="item.id"-->
<!--                :key="item.id"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.adjustSchoolId"-->
<!--              placeholder="调剂学校"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option-->
<!--                v-for="item in schoolList"-->
<!--                :label="item.deptName"-->
<!--                :value="item.id"-->
<!--                :key="item.id"-->
<!--              ></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
              size="small"
              v-model="search.type"
              placeholder="类别"
              clearable
            >
              <el-option
                v-for="(item, index) in typeList"
                :label="item"
                :value="item"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.nature"
              placeholder="学校性质"
              clearable
            >
              <el-option label="乡镇学校" :value="1"></el-option>
              <el-option label="城区学校" :value="2"></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.schoolReviewStatus"-->
<!--              placeholder="学校审核状态"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="1"></el-option>-->
<!--              <el-option label="通过" :value="2"></el-option>-->
<!--              <el-option label="驳回-修改信息" :value="3"></el-option>-->
<!--              <el-option label="驳回-不可再报" :value="4"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.educationReviewStatus"-->
<!--              placeholder="教育局审核状态"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="1"></el-option>-->
<!--              <el-option label="通过" :value="2"></el-option>-->
<!--              <el-option label="驳回-修改信息" :value="3"></el-option>-->
<!--              <el-option label="驳回-不可再报" :value="4"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.estateReviewStatus"-->
<!--              placeholder="房管局审核状态"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="1"></el-option>-->
<!--              <el-option label="通过" :value="2"></el-option>-->
<!--              <el-option label="不通过" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.publicSecurityReviewStatus"-->
<!--              placeholder="公安审核状态"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="待审核" :value="1"></el-option>-->
<!--              <el-option label="通过" :value="2"></el-option>-->
<!--              <el-option label="不通过" :value="3"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.admissionLetterInStatus"-->
<!--              placeholder="是否发送录取通知书"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="已发送" :value="1"></el-option>-->
<!--              <el-option label="未发送" :value="0"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-select-->
<!--              size="small"-->
<!--              v-model="search.checkInStatus"-->
<!--              placeholder="是否报到"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="已报到" :value="1"></el-option>-->
<!--              <el-option label="未报到" :value="0"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
          <el-form-item>
            <el-select
                size="small"
                v-model="search.spanStatus"
                placeholder="跨区调剂状态"
                class="sd-w-130"
                clearable
            >
              <el-option label="未调剂" :value="0"></el-option>
              <el-option label="已调剂" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.schoolType"
                placeholder="学校类别"
                class="sd-w-130"
                clearable
            >
              <el-option label="公办" :value="1"></el-option>
              <el-option label="民办" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      v-loading="tableLoading"
      @selection-change="handleSelectionChange"
    >
<!--      <el-table-column align="center" type="selection" width="50" :fixed="true">-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="是否公示"-->
<!--        prop="publicityStatus"-->
<!--        width="85"-->
<!--      >-->
<!--      </el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="是否发送录取通知书"-->
<!--        prop="admissionLetterInStatus"-->
<!--        width="95"-->
<!--      >-->
<!--      </el-table-column>-->
      <el-table-column
        align="center"
        label="报名ID"
        prop="enrollId"
        width="140"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
        width="120"
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="studentIdCardNumber"
        width="170"
      ></el-table-column>
      <el-table-column
        align="center"
        label="类别"
        prop="type"
        width="300"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        label="购房时间"
        prop="purchaseTime"
        width="100"
      ></el-table-column> -->
      <el-table-column
        align="center"
        label="报名学校"
        prop="enrollSchoolName"
        width="180"
      >
      </el-table-column>
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="调剂学校"-->
<!--        prop="adjustSchoolName"-->
<!--        width="180"-->
<!--      ></el-table-column>-->
      <el-table-column
        align="center"
        label="报名时间"
        prop="enrollTime"
        width="160"
      ></el-table-column>
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="是否优抚对象"-->
<!--        prop="isEntitledGroup"-->
<!--        width="110"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="优抚类型"-->
<!--        prop="entitledGroupType"-->
<!--      ></el-table-column>-->
<!--      <div v-if="deptCode=='130424'">-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="电力局审核状态"-->
<!--            prop="statusDl"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusDl==1?'待审核':scope.row.statusDl==2?'通过':scope.row.statusDl==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="公安局审核状态"-->
<!--            prop="statusGa"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusGa==1?'待审核':scope.row.statusGa==2?'通过':scope.row.statusGa==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="水利局审核状态"-->
<!--            prop="statusSl"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusSl==1?'待审核':scope.row.statusSl==2?'通过':scope.row.statusSl==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="司法局审核状态"-->
<!--            prop="statusSf"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusSf==1?'待审核':scope.row.statusSf==2?'通过':scope.row.statusSf==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="行政审批局审核状态"-->
<!--            prop="statusXz"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusXz==1?'待审核':scope.row.statusXz==2?'通过':scope.row.statusXz==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="住建局审核状态"-->
<!--            prop="statusZj"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusZj==1?'待审核':scope.row.statusZj==2?'通过':scope.row.statusZj==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="资规局审核状态"-->
<!--            prop="statusZg"-->
<!--            width="85"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <span>{{scope.row.statusZg==1?'待审核':scope.row.statusZg==2?'通过':scope.row.statusZg==3?'不通过':''}}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--      </div>-->
<!--      <div v-else>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="房产审核"-->
<!--            prop="estateReviewStatus"-->
<!--            width="85"-->
<!--        ></el-table-column>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="公安审核"-->
<!--            prop="publicSecurityReviewStatus"-->
<!--            width="85"-->
<!--        ></el-table-column>-->
<!--      </div>-->
<!--        <el-table-column-->
<!--            align="center"-->
<!--            label="学校审核状态"-->
<!--            prop="schoolReviewStatus"-->
<!--            width="85"-->
<!--        ></el-table-column>-->

<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="教育局审核状态"-->
<!--        prop="educationReviewStatus"-->
<!--        width="85"-->
<!--      ></el-table-column>-->
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="是否报到"-->
<!--        prop="checkInStatus"-->
<!--        width="85"-->
<!--      ></el-table-column>-->
      <el-table-column
          align="center"
          label="跨区调剂状态"
          prop="adjustmentText"
          width="130"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="跨区调剂原因"
          prop="adjustmentReason"
          width="130"
      >
      </el-table-column>
<!--      <el-table-column-->
<!--        align="center"-->
<!--        label="备注"-->
<!--        prop="remark"-->
<!--        show-overflow-tooltip-->
<!--      ></el-table-column>-->
      <el-table-column align="center" label="操作" width="360" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
            icon="el-icon-view"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="detail(row, $index)"
            >详情</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="buChongCaiLiao(row)"
          >补充材料</el-link
          >
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="liuZhuan(row)"
          >流转记录</el-link
          >
          <el-link
            icon="el-icon-refresh"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            @click="adjust(row)"
            >调剂</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 调剂 -->
    <el-dialog
      title="调剂"
      :visible.sync="modal.adjust"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="adjustForm"
        ref="adjustForm"
        :rules="adjustRules"
        label-position="right"
        label-width="150px"
      >
        <el-form-item label="报名ID：">{{ adjustInfo.enrollId }}</el-form-item>
        <el-form-item label="学生姓名：">{{
          adjustInfo.studentName
        }}</el-form-item>
        <el-form-item label="报名学校：">{{
          adjustInfo.enrollSchoolName
        }}</el-form-item>
        <el-form-item prop="toDeptId" label="调剂区县选择：">
          <el-select
              v-model="adjustForm.toDeptId"
              style="width: 300px"
              @change="tiaoJiQuXian"
          >
            <el-option
                v-for="item in quXianListAvailable"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="toSchoolId" label="调剂学校选择：">
          <el-select
            v-model="adjustForm.toSchoolId"
            style="width: 300px"

          >
<!--            @change="adjustSchoolChange"-->
            <el-option
              v-for="item in schoolListAvailable"
              :label="item.deptName"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="content" label="调剂原因：">
          <el-input type="textarea" style="width:300px" placeholder="请输入调剂原因" v-model="adjustForm.content"></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('adjust', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmAdjust"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!--补充材料-->
    <el-dialog
        title="补充材料"
        :visible.sync="buChongShow"
        width="60%"
    >
      <div v-if="imgList.length>0" style="display: flex;justify-content: space-between">
        <!--        <span>{{imgQianZhui+item}}</span>-->
        <el-image v-for="(item, index) in imgList"
                  style="width: 200px; height: 200px"
                  :src="imgQianZhui+item"
                  :preview-src-list="[imgQianZhui+item]"
                  :key="index"
        ></el-image>
      </div>

    </el-dialog>
    <!--流转记录-->
    <el-dialog
        title="流转记录"
        :visible.sync="liuZhuanShow"
        width="60%"
    >
      <el-table
          :data="liuZhuanData"
          style="width: 100%"
      >
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作账号"
            prop="creatorName"
            width="140"
        ></el-table-column>
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作人绑定微信昵称"
            prop="nickName"
            width="160"
        ></el-table-column>
        <!--      <el-table-column-->
        <!--          align="center"-->
        <!--          label="绑定微信号"-->
        <!--          prop=""-->
        <!--          width="140"-->
        <!--      ></el-table-column>-->
        <el-table-column
            v-if="role=='CITY_ADMIN'"
            align="center"
            label="操作时间"
            prop="createTime"
            width="140"
        ></el-table-column>
        <el-table-column
            align="center"
            label="报名ID"
            prop="signId"
            width="140"
        ></el-table-column>
        <el-table-column
            align="center"
            label="学生姓名"
            prop="studentName"
            width="120"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="身份证号"
            prop="idCard"
            width="170"
        ></el-table-column>
        <el-table-column
            align="center"
            label="多胞胎数量"
            prop="tiwnsNum"
            width="85"
        ></el-table-column>
        <el-table-column
            align="center"
            label="原报名学校"
            prop="fromSchoolName"
            width="180"
        >
        </el-table-column>

        <el-table-column
            align="center"
            label="原报名区县"
            prop="fromDeptName"
            width="180"
        ></el-table-column>
        <!--          <el-table-column-->
        <!--            align="center"-->
        <!--            label="调剂状态"-->
        <!--            prop=""-->
        <!--            width="180"-->
        <!--        ></el-table-column>-->
        <!--          <el-table-column-->
        <!--              align="center"-->
        <!--              label="调剂区县"-->
        <!--              prop=""-->
        <!--              width="180"-->
        <!--          ></el-table-column>-->
        <el-table-column
            align="center"
            label="调入学校"
            prop="toSchoolName"
            width="180"
        ></el-table-column>
        <el-table-column
            align="center"
            label="调剂原因"
            prop="content"
            width="180"
        ></el-table-column>
      </el-table>
      <div class="page-container" v-if="liuZhuanTotal > 0">
        <el-pagination
            background
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page.sync="liuZhuanForm.pageNumber"
            layout="total, prev, pager, next, sizes"
            :page-sizes="$pageSizes"
            :total="liuZhuanTotal"
        >
        </el-pagination>
      </div>
      <span
          slot="footer"
          class="dialog-footer"
      >
            <el-button @click="liuZhuanShow = false">取 消</el-button>
          </span>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog
      :visible.sync="modal.stuDetail"
      :close-on-click-modal="false"
      :deptId="deptId"
      title="学生报名详情"
      center
      width="1240px"
      @close="stuDetailClose"
    >
      <enroll-detail
        :stu-detail="curStuDetail"
        :deptCode="deptCode"
        :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div class="flex-center" style="margin-top: 20px">
<!--        <el-button type="primary" @click="nextEnrollDetail" size="small"-->
<!--          >下一条</el-button-->
<!--        >-->
        <el-button size="small" type="info" @click="stuDetailClose"
          >关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getEnrollList,
  getTypeList,
  report,
  batchAudit,
  publicity,
  publicAdmissionResults,
  sendRequisition,
  updateEntitledGroup,
} from "@/api/enrollment";
import { getDepts } from "@/api/common";
import { pref, priorityTypeList } from "@/utils/common";
import EnrollDetail from "@/components/biaoZhunBanXiaoXueStudentList/EnrollDetail";
import {biaoZhunTiaoChu, getKuaQuBuChong, kuaQuJiLu} from "@/api/tiaoChu";
export default {
  mixins: [TableMixin, ModalMixin],
  components: { EnrollDetail },
  props: {
    deptCode:{
      type: String,
      default: ''},
    deptId:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      prefixDeptCode: this.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      buChongShow:false,
      liuZhuanShow:false,
      liuZhuanForm:{
        pageNumber: 1,
        pageSize: 10,
      },
      liuZhuanData:[],
      liuZhuanTotal:0,
      imgQianZhui:`${process.env.VUE_APP_BASE_API}/user-api`,
      imgList:[],
      search: {
        enrollStage: "2",
        enrollId: "",
        studentName: "",
        schoolType:1,
        // houseDate: "",
        createdTime: "",
        endTime: "",
        enrollSchoolId: "",
        adjustSchoolId: "",
        type: "",
        nature: "",
        schoolReviewStatus: "",
        educationReviewStatus: "",
        estateReviewStatus: "",
        publicSecurityReviewStatus: "",
        checkInStatus: "",
        admissionLetterInStatus: "",
      },
      typeList: [],
       quXianListAvailable:[],
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        stuDetail: false,
      },
      schoolList: [],
      multipleSelection: [],
      // 驳回
      rejectForm: {
        id: 0,
        reviewReason: "",
        reviewStatus: "",
      },
      rejectRules: {
        reviewStatus: [
          {
            required: true,
            message: "请选择驳回类型",
            trigger: "change",
          },
        ],
        reviewReason: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 调剂
      adjustForm: {
        id: "",
        toSchoolId: "",
        toDeptId:'',
        adjustSchoolName: "",
        content:''
      },
      adjustRules: {
        toSchoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
        toDeptId:[
          {
            required: true,
            message: "请选择调剂区县",
            trigger: "change",
          },
        ],
        content: [{
          required: true,
          message: "请输入调剂原因",
          trigger: "blur",
        },
          {required: true, min: 2, max: 500, message: '请输入2-500个文字', trigger: 'blur'}
        ]
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      // 报到
      reportForm: {
        id: "",
        noShowCause: "",
        reportStatus: "",
      },
      reportRules: {
        reportStatus: [
          {
            required: true,
            message: "请选择报到类型",
            trigger: "change",
          },
        ],
        noShowCause: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 发送录取通知书
      noticeForm: {
        content: "",
        num: "",
      },
      noticeRules: {
        content: [
          {
            required: true,
            message: "请输入录取通知书内容",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 优抚类型
      priorityForm: {
        id: "",
        priorityType: "",
        priorityTypeStr: "",
      },
      priorityRules: {
        priorityType: [
          {
            required: true,
            message: "选择优抚类型",
            trigger: "change",
          },
        ],
        priorityTypeStr: [
          {
            required: true,
            message: "输入其他类型",
            trigger: "blur",
          },
        ],
      },
      priorityInfo: {
        studentName: "",
        studentIdCardNumber: "",
      },
      priorityTypeList: priorityTypeList,
      auditStatusList: [],
      loadingAuditStatus: true,
      // 当前学生报名详情
      curStuDetail: {},
      // 下一条报名详情index
      index: 0,
      schoolListAvailable:[]
    };
  },
  computed: {
    // schoolListAvailable() {
    //   return this.schoolList.filter(
    //     (item) =>
    //       item.deptName !=
    //       (this.adjustInfo.adjustSchoolName
    //         ? this.adjustInfo.adjustSchoolName
    //         : this.adjustInfo.enrollSchoolName)
    //   );
    // },
  },
  async created() {
      // 区县级 获取学校列表
      this.getDepts();

    // 查询报名类别
    this.getTypeList();
    this.getQuXianList()
  },
  methods: {
    buChongCaiLiao(row){
      this.buChongShow=true
      // this.form.studentId=row.id
      // this.form.deptId=row.toDeptId deptId: row.toDeptId,
      // this.form.schoolId=row.schoolId
      getKuaQuBuChong({studentId: row.studentBaseId,deptId:row.deptId}).then(res=>{
        // this.$refs.mediaUpload.upload.list=res
        if(res.material!=null){
          this.imgList=res.material.split(',')
        }else{
          this.imgList=[]
        }
        console.log(res, this.imgList,"res")
      })
    },
    handleSizeChange1(size) {
      this.liuZhuanForm.pageSize = size
      this.liuZhuanForm.pageNumber = 1
      this.liuZhuan1()
    },
    handleCurrentChange1(page) {
      this.liuZhuanForm.pageNumber = page
      this.liuZhuan1()
    },
    liuZhuan(row){   this.liuZhuanShow=true
      this.liuZhuanForm.studentId=row.studentBaseId
      this.liuZhuanForm.deptId=row.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
      })
    },
    liuZhuan1(){
      // this.liuZhuanShow=true
      // this.liuZhuanForm.studentId=row.id
      // this.liuZhuanForm.deptId=this.deptId
      kuaQuJiLu(this.liuZhuanForm).then(res=>{
        this.liuZhuanData=res.records
        this.liuZhuanTotal=Number(res.total)
        // this.liuZhuanTotal=res.total
      })
    },
   async getQuXianList(){
      let depts = await getDepts({ level: 2 });
      this.quXianListAvailable=depts.filter((item) => item.deptCode!='130426');
    },
    tiaoJiQuXian(e){
      console.log(e,"e")
      this.getSchoolList1(this.adjustForm.toDeptId)
    },
    getSchoolList1(item){
      console.log(item,"getters")
      getDepts({type:1,period:2,level:3,parentId:item},this.prefixDeptCode).then(res=>{
        this.schoolListAvailable=res
      })
    },
    // 列表
    getTableData() {
      this.tableLoading = true;
      getEnrollList(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取报名类别 2小学 3初中
    getTypeList() {
      getTypeList({ key: 2 }, this.prefixDeptCode).then((res) => {
        this.typeList = res;
      });
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: "2",
        parentId: this.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 调剂
    adjust(row) {
      this.switchModal("adjust", true);
      this.$nextTick(() => {
        this.$refs.adjustForm.resetFields();
        this.adjustForm.studentId = row.studentBaseId;
        this.adjustForm.adjustSchoolId = "";
        this.adjustForm.adjustSchoolName = "";

        this.adjustInfo.enrollId = row.enrollId;
        this.adjustInfo.studentName = row.studentName;
        this.adjustInfo.enrollSchoolName = row.enrollSchoolName;
        this.adjustInfo.adjustSchoolName = row.adjustSchoolName;
      });
    },
    // 调剂确定
    confirmAdjust() {
      this.$refs.adjustForm.validate((valid) => {
        if (valid) {
          biaoZhunTiaoChu(this.adjustForm, this.prefixDeptCode).then(() => {
            this.$message.success("操作成功");
            this.switchModal("adjust", false);
            this.getTableData();
          });
        }
      });
    },
    // 调剂学校选择change
    adjustSchoolChange() {
      // this.schoolList.forEach((item) => {
      //   if (this.adjustForm.adjustSchoolId == item.id) {
      //     this.adjustForm.adjustSchoolName = item.deptName;
      //   }
      // });
    },
    // 报到
    report(row) {
      this.switchModal("report", true);
      this.$nextTick(() => {
        this.$refs.reportForm.resetFields();
        this.reportForm.id = row.studentBaseId;
        this.reportForm.reportStatus = "";
        this.reportForm.noShowCause = "";
      });
    },
    // 报到确定
    confirmReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          report(this.reportForm, this.prefixDeptCode).then(() => {
            this.$message.success("操作成功");
            this.switchModal("report", false);
            this.getTableData();
          });
        }
      });
    },
    // 优抚类型
    priority(row) {
      this.switchModal("priority", true);
      this.$nextTick(() => {
        this.$refs.priorityForm.resetFields();
        this.priorityForm.id = row.studentBaseId;
        this.priorityForm.entitledGroupType = "";
        this.priorityInfo.studentName = row.studentName;
        this.priorityInfo.studentIdCardNumber = row.studentIdCardNumber;
      });
    },
    // 优抚类型确定
    confirmPriority() {
      this.$refs.priorityForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.priorityForm.id,
            entitledGroupType:
              this.priorityForm.priorityType == "其他-填写其他类型"
                ? this.priorityForm.priorityTypeStr
                : this.priorityForm.priorityType,
          };
          updateEntitledGroup(params, this.prefixDeptCode).then(() => {
            this.$message.success("操作成功");
            this.switchModal("priority", false);
            this.getTableData();
          });
        }
      });
    },
		// 前往添加报名信息
		go2AddAd() {
			this.$router.push({
				path: '/enrollment/addAd',
				query: {
					period: 'primary'
				}
			})
		},
    // 导出报名信息
    exportEnrollInfo() {
      this.$download(
        `${pref}${this.prefixDeptCode}/biz/excelConfig/exportExcelPriMiddle`,
        this.search,
        "xlsx",
        "导出报名信息.xlsx"
      ).then(() => {
        this.$message.success("导出成功");
      });
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 下载入户调查单
    exportHouseholdSurvey() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = { search: this.search, list: checkList };
      this.$download(
        `${pref}${this.prefixDeptCode}/biz/recruitStudent/batchExportHouseholdSurvey`,
        params,
        "zip",
        "下载入户调查单.zip"
      ).then(() => {
        this.$message.success("下载成功");
      });
    },
    // 批量审核通过
    batchAudit() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = {
        search: Object.assign({}, this.search, {
          pageSize: undefined,
          pageNumber: undefined,
        }),
        list: checkList,
      };
      let msg =
        checkList.length == 0
          ? "确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请谨慎使用。"
          : "确认为已选学生完成审核吗";
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        batchAudit(params, this.prefixDeptCode).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 公示录取结果
    publicAdmission() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = {
        search: Object.assign({}, this.search, {
          pageSize: undefined,
          pageNumber: undefined,
        }),
        list: checkList,
      };
      publicity(params, this.prefixDeptCode).then((res) => {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          publicAdmissionResults(params, this.prefixDeptCode).then(() => {
            this.$message.success("操作成功");
            this.getTableData();
          });
        });
      });
    },
    // 发送录取通知书
    notice() {
      this.switchModal("notice", true);
      this.$nextTick(() => {
        this.$refs.noticeForm.resetFields();
        this.noticeForm.content = "";
        this.noticeForm.num = "";
        let params = {
          search: Object.assign({}, this.search, {
            pageSize: undefined,
            pageNumber: undefined,
          }),
          type: 1,
          content: this.noticeForm.content,
        };
        sendRequisition(params, this.prefixDeptCode).then((res) => {
          this.noticeForm.num = res;
        });
      });
    },
    // 发送录取通知书确定
    confirmNotice() {
      this.$refs.noticeForm.validate((valid) => {
        if (valid) {
          let params = {
            search: Object.assign({}, this.search, {
              pageSize: undefined,
              pageNumber: undefined,
            }),
            type: 2,
            content: this.noticeForm.content,
          };
          sendRequisition(params, this.prefixDeptCode).then((res) => {
            this.$confirm("录取通知书成功发送 " + res + " 人", "提示", {
              confirmButtonText: "关闭",
              showCancelButton: false,
              type: "success",
              closeOnClickModal: false,
              showClose: false,
              closeOnPressEscape: false,
            }).then(() => {
              this.switchModal("notice", false);
              this.getTableData();
            });
          });
        }
      });
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      // this.getAuditStatus();
    },
    // 查询审核情况
    // getAuditStatus() {
    //   this.auditStatusList = [];
    //   this.loadingAuditStatus = true;
    //   getAuditStatus(
    //     { key: this.curStuDetail.studentBaseId },
    //     this.prefixDeptCode
    //   )
    //     .then((res) => {
    //       this.auditStatusList = res;
    //     })
    //     .finally(() => {
    //       this.loadingAuditStatus = false;
    //     });
    // },
    // 详情 - 下一条
    nextEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
        this.search.pageNumber * this.search.pageSize > this.total ||
        this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          // this.getAuditStatus();
        } else {
          this.$message.error("已是最后一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          // this.getAuditStatus();
        } else {
          this.search.pageNumber += 1;
          getEnrollList(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.curStuDetail = this.tableData.records[0];
            this.index = 0;
            // this.getAuditStatus();
          });
        }
      }
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}
.notice-num {
  font-size: 16px;
}
</style>
