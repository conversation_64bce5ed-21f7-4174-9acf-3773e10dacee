<!--

倒计时使用方法：
	<timer
		:total="总秒数"
		ref="timer"
		@start="倒计时开始的回调A"
		@ongoing="倒计时进行中的回调B"
		@end="倒计时结束的回调C"
	></timer>


	手动调用_startTimer来开启倒计时：this.$refs['timer']._startTimer()
	触发回调A
		
	倒计时进行中，触发回调B：
	B(remain) {
		// remain即剩余秒数
	}
	
	剩余秒数为0时自动结束倒计时
	或手动调用_endTimer来主动结束倒计时：this.$refs['timer']._endTimer()
	都会触发回调C
	
-->
<template></template>
<script>
export default {
	name: 'timer',
	data() {
		return {
			timer: 0
		}
	},
	props: {
		// 总秒数
		total: {
			required: true,
			type: Number
		}
	},
	methods: {
		// 开始
		_startTimer() {
			clearTimeout(this.timer)
			this.$emit('start')
			// 开启循环
			this._ongoing(this.total)
		},
		// 进行中
		_ongoing(remain) {
			let that = this
			that.timer = setTimeout(function () {
				remain--
				if (remain == 0) {
					that._endTimer()
				} else {
					that.$emit('ongoing', remain)
					// 继续调用自己
					that._ongoing(remain)
				}
			}, 1000)
		},
		// 结束
		_endTimer() {
			clearTimeout(this.timer)
			this.$emit('end')
		}
	}
}
</script>

<style>

</style>