<!-- 组件用途 -->
<template>
    <div h-full flex flex-justify-between flex-items-center>
        <el-link type="primary" href="/" m-l-2 m-r-2 :underline="false">
            <div flex>
                <img :src="require(`@/assets/logo.png`)" w-8 h-8 m-r-2 />
                <span c-white font-size-6>{{ siteName }}</span>
            </div>
        </el-link>
        <div flex-center c-white>
            <!-- <el-avatar m-r-2 :size="30" icon="el-icon-user-solid" /> -->
            <span m-r-4>欢迎您，{{ username }}</span>
            <el-link type="warning" font-size-4 m-r-2 @click="logout" :underline="false">退出</el-link>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    name: 'LmtHeader',
    data() {
        return {
            logo: ''
        }
    },
    async created() {
        let img = await import('../../assets/logo.png')
        this.logo = img.default
    },
    computed: {
        ...mapGetters(['siteName', 'userInfo']),
        username() {
            return this.userInfo?.nickname || ''
        }
    },
    methods: {
        async logout() {
            await this.$store.dispatch('user/logout')
            await this.$store.dispatch('settings/clearState')
            await this.$router.push('/login')
        },
        async imageUrl() {
            return new URL(`@/assets/logo.png`, import.meta.url).href
        }
    }
}



</script>

<style lang="scss" scoped></style>