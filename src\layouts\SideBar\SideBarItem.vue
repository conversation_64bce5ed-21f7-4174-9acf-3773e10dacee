<template>
  <!-- 只有一个子菜单并且默认跳转子菜单 -->
  <el-menu-item
    v-if="
      menuItem.children && menuItem.children.length == 1 && menuItem.redirect
    "
    :index="indexPath"
    :id="indexPath"
  >
    <span> {{ menuItem.meta.title }}</span>
  </el-menu-item>
  <!-- 只有一个子菜单不默认跳转子菜单 -->
  <el-submenu
    v-else-if="menuItem.children && menuItem.children.length > 1"
    :id="menuItem.path"
    :index="menuItem.path"
  >
    <template slot="title">
      <span> {{ menuItem.meta.title }}</span>
    </template>
    <!-- v-for="item in menuItem.children.filter(
        (v) => !v.meta.hidden && v.meta.roles.indexOf(this.role) >= 0
      )" -->
    <side-bar-item
      v-for="item in routeItems(menuItem)"
      :key="item.path"
      :menuItem="item"
      :parentRoute="menuItem"
    ></side-bar-item>
  </el-submenu>
  <!-- 子菜单 -->
  <el-menu-item v-else :index="indexPath" :id="indexPath">
    <span> {{ menuItem.meta.title }}</span>
  </el-menu-item>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "SideBarItem",
  data() {
    return {
      role: this.$store.getters.role,
      isFive: this.$store.getters.isFive,
      deptCode: this.$store.getters.deptCode,
      period: this.$store.getters.period,
      privateeStatus:this.$store.getters.userInfo.privateeStatus,
      type:this.$store.getters.userInfo.deptInfo!=null?this.$store.getters.userInfo.deptInfo.type:0
    };
  },
  props: {
    menuItem: {
      type: Object,
      default: () => {},
    },
    parentRoute: {
      type: Object,
      default: () => undefined,
    },
  },
  computed: {
    ...mapGetters(["openMenus"]),
    routeItems() {
      return (menuItem) =>
        menuItem.children.filter(
          (v) =>
            v.meta?.hidden == false &&
            v.meta.roles.indexOf(this.role) >= 0 &&
            (v.meta.isFive == undefined || v.meta.isFive == this.isFive) &&
            (v.meta.depts == undefined ||
              v.meta.depts.indexOf(this.deptCode) >= 0) &&
            (v.meta.period == undefined ||
              v.meta.period.indexOf(this.period) >= 0)&&
             (((v.meta.privateeStatus==this.privateeStatus||v.meta.privateeStatus==undefined||v.meta.privateeStatus==3)&&this.role!='SCHOOL')||
              ((v.meta.privateeStatus==this.privateeStatus||v.meta.privateeStatus!=3)&&this.role=='SCHOOL'&&this.type!=1)||(((v.meta.privateeStatus==3||v.meta.privateeStatus==undefined))&&this.role=='SCHOOL'&&this.type!=2))
        );
    },
    indexPath() {
      if (this.menuItem.children && this.menuItem.children.length == 1) {
        if (this.menuItem.redirect) {
          const path = this.menuItem.path;
          if (path[path.length - 1] == "/") {
            return this.menuItem.path + this.menuItem.children[0].path;
          } else {
            return this.menuItem.path + "/" + this.menuItem.children[0].path;
          }
        } else {
          const path = this.menuItem.path;
          if (path[path.length - 1] == "/") {
            return this.menuItem.path + this.menuItem.children[0].path;
          } else {
            return this.menuItem.path + "/" + this.menuItem.children[0].path;
          }
        }
      } else {
        let path = this.menuItem.path;
        let parent = this.parentRoute;
        do {
          if (parent) {
            path = parent.path + "/" + path;
          }
          parent = parent.parentRoute || null;
        } while (parent);
        return path;
      }
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.sd-el-menu {
  .el-menu-item i {
    color: var(--el-menu-text-color) !important;
  }

  .el-menu-item.is-active i {
    color: var(--el-menu-active-color) !important;
  }
}
</style>
<style lang="scss">
.sd-el-menu {
  .el-submenu__title i {
    color: var(--el-menu-hover-text-color) !important;
  }

  .el-submenu__title:hover {
    background-color: var(--el-menu-hover-bg-color) !important;
    color: var(--el-menu-hover-text-color) !important;
  }

  .el-menu-item:hover {
    background-color: var(--el-menu-hover-bg-color) !important;
    color: var(--el-menu-hover-text-color) !important;
  }
}
</style>
