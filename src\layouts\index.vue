<!-- 组件用途 -->
<template>
  <div class="layouts-container">
    <div class="sd-header">
      <LmtHeader />
    </div>
    <div class="sd-main-container">
      <div class="sd-side-container">
        <LmtSideBar />
      </div>
      <div class="sd-content-container">
        <!-- <el-tabs class="sd-tabs" v-model="activeName" type="card" @tab-click="handleClick" @edit="handleTabsEdit">
          <el-tab-pane v-for="(item, index) in openMenus" :label="item.label" :name="item.path" :key="index"
            :closable="item.closable">
          </el-tab-pane>
        </el-tabs>
        <div class="sd-router-container" overflow-x-auto>
          <keep-alive :include="keepAliveComponents">
            <router-view />
          </keep-alive>
        </div> -->
				<router-view />
      </div>
    </div>
  </div>
</template>

<script>
import LmtHeader from "@/layouts/LmtHeader/LmtHeader.vue";
import LmtSideBar from "@/layouts/SideBar/SideBar.vue";
import { mapGetters } from 'vuex';

export default {
  name: "Layouts",
  components: { LmtHeader, LmtSideBar },
  data() {
    return {
      activeName: '/index',
    }
  },
  computed: {
    ...mapGetters(['openMenus']),
    /* keepAliveComponents() {
      return this.openMenus.map(item => item.name)
    } */
  },
  mounted() {
    /* if (!this.openMenus.some(item => ['/index', '/'].includes(item.path))) {
      const menus = [{ path: '/index', label: '首页', closable: false, name: 'Index' }]
      this.$store.dispatch('settings/setOpenMenus', menus)
      this.activeName = '/index'
    } else {
      this.activeName = this.$route.path
    } */
  },
  watch: {
    '$route.path'(val) {
      if (!['/', "/index"].includes(val)) {
        const routes = this.$router.match(val) || []
        let path = '', label = '', name = ''
        for (let i = routes.matched.length - 1; i >= 0; i--) {
          if (!path) {
            path = routes.matched[i].path
          }
          if (!label) {
            label = routes.matched[i].meta.title
          }
          if (!name) {
            name = routes.matched[i].name
          }
        }
        const menu = { path, label, closable: true, name }
        if (!this.openMenus.some(item => item.path == path)) {
          const menus = [...this.openMenus, menu]
          this.$store.dispatch('settings/setOpenMenus', menus)
        }
      }
      this.activeName = val
    }
  },
  methods: {
    handleClick(tab) {
      this.$router.push({ path: tab.name })
    },
    handleTabsEdit(targetName, action) {
      if (action == 'remove') {
        console.log(targetName, action)
        const menus = [...this.openMenus]
        const index = menus.findIndex(item => item.path == targetName)
        menus.splice(index, 1)
        this.$store.dispatch('settings/setOpenMenus', menus)
        this.$store.dispatch('settings/removeParams', targetName)
        const menu = menus[index - 1]
        this.$router.push(menu.path)
      }
    }
  }
}


</script>

<style lang="scss" scoped>
.sd-header {
  height: var(--header-height);
  background-color: var(--sd-header-background-color);
  box-shadow: 0 1px 4px rgb(54, 54, 54);
  z-index: 100;
}

.sd-main-container {
  display: flex;
}

.sd-side-container {
  width: var(--sd-sidebar-width);
  background-color: var(--sd-sidebar-background-color);
  height: calc(100vh - var(--header-height));
  box-shadow: 0 1px 4px rgb(54, 54, 54);
  z-index: 99;
}

.sd-content-container {
  flex: 1;
  padding: 15px;
  width: calc(100% - var(--sd-sidebar-width));
  height: calc(100vh - var(--header-height) - 30px);
  overflow: auto;
}

.layouts-container {
  min-width: 1000px;
}
</style>
<style lang="scss">
.sd-tabs {
  .el-tabs__item.is-top {
    height: 28px !important;
    line-height: 28px !important;
  }

  .el-tabs__header.is-top {
    margin: 0 0 10px !important;
  }
}

.sd-router-container {
  height: calc(100vh - var(--header-height) - 82px);
  overflow: auto;
}
</style>
