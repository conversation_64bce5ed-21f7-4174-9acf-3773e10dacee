export default {
    data() {
        return {
            tableLoading: false,
            tableData: {
							list: [],
							pageNumber: 1,
							pageSize: 10,
							total: 0
            },
            search: {
							pageNumber: 1,
							pageSize: 10
            },
            dialogFormComponent: {
                name: null,
                data: null,
                mode: null,
                dialogFormVisible: false,
                saveLoading: false,
                title: () => {
                    const name = this.dialogFormComponent.name
                    if (name && name.indexOf('Add') > -1) {
                        return '新增'
                    } else if (name && name.indexOf('Edit') > -1) {
                        return '编辑'
                    } else if (name && name.indexOf('View') > -1) {
                        return '详情'
                    } else if (name && name.indexOf('Import') > -1) {
                        return '批量导入'
                    } else if (name && name.indexOf('Permit') > -1) {
                        return '分配权限'
                    } else {
                        return ""
                    }
                },
                show: (name, data) => {
                    this.dialogFormComponent.name = name
                    this.dialogFormComponent.data = data
                    this.dialogFormComponent.saveLoading = false
                    this.dialogFormComponent.dialogFormVisible = true
                },
                close: () => {
                    this.dialogFormComponent.name = ""
                    this.dialogFormComponent.data = null
                    this.dialogFormComponent.mode = null
                    this.dialogFormComponent.saveLoading = false
                    this.dialogFormComponent.dialogFormVisible = false
                },
                save: () => {
                    this.$refs[this.dialogFormComponent.name] &&
                        this.$refs[this.dialogFormComponent.name].save &&
                        this.$refs[this.dialogFormComponent.name].save()
                    // this.dialogFormComponent.saveLoading = true
                },
                saveComplete: (state) => {
                    console.log("saveComplete", state)
                    this.dialogFormComponent.saveLoading = false
                    if (state == 1) {
                        this.dialogFormComponent.name = ""
                        this.dialogFormComponent.data = null
                        this.dialogFormComponent.dialogFormVisible = false
                        this.getTableData()
                        this.$notify.success({ message: '保存成功', title: '温馨提示' })
                    }
                }
            },
            routePath: this.$route.fullPath
        }
    },
		computed: {
			total() {
					return this.tableData && this.tableData.total ? Number(this.tableData.total) : 0
			}
		},
    mounted() {
        if (this.$store.state.settings && this.$store.state.settings.params) {
            const search = this.$store.state.settings.params[this.$route.path] || undefined
            if (search) {
                this.search = { ...search }
            }
        }
        window.addEventListener('beforeunload', this.handleBeforeUnload)
        this.getTableData()
    },
    methods: {
        handleBeforeUnload() {
            this.$store.dispatch('settings/setParams', { key: this.$route.path, value: this.search })
        },
        getTableIndex(index) {
            return (this.search.pageNumber - 1) * this.search.pageSize + index + 1
        },
        getTableData() {
            console.error("请实现 getTableData 方法")
        },
        searchSubmit() {
					this.search.pageNumber = 1
          this.getTableData()
        },
        showEditForm(data = null) {
            this.dialogFormComponent.name = 'EditForm'
            this.dialogFormComponent.mode = 'EDIT'
            this.dialogFormComponent.show('EditForm', data)
        },
        showAddForm() {
            this.dialogFormComponent.name = 'AddForm'
            this.dialogFormComponent.mode = 'ADD'
            this.dialogFormComponent.show('AddForm', null)
        },
        showDeleteForm() {
            console.error("请实现 showDeleteForm 方法")
        },
        handleSizeChange(size) {
            this.search.pageSize = size
            this.search.pageNumber = 1
            this.getTableData()
        },
        handleCurrentChange(page) {
            this.search.pageNumber = page
            this.getTableData()
        }
    },
    destroyed() {
        window.removeEventListener('beforeunload', this.handleBeforeUnload)
    }
}
