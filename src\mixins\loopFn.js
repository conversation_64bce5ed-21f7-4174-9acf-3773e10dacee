import { rulesList, triggerTpByCode } from "@/utils/dictionary"
import { idCardReg, idCardValidator, mobileValidator } from "@/utils/validator.js"
// 循环表单通用方法
let LoopFn = {
	data() {
		return {
			// 加载中
			loadingAdForm: true,
			// 所有表单项
			form: {},
			// 所有验证规则
			rules: {},
			// 整个页面循环用的list
			originList: [],
			// 非房产非双胞胎的普通字段
			isGuardianDivorced: false,
			normalForm: [],
			// 房产
			propertyForm: {
				// 当前选中tab
				tp: 0,
				// 上次选中tab
				lastTp: 0,
				// 所有房产类型list
				list: [],
				// 当前类型房产字段
				curList: [],
			},
			// 优抚对象
			youFuForm: {
				// 所有优抚对象list
				list: [],
				// 当前类型优抚对象字段
				curList: [],
				// 上次选中tab
				lastTp: 0,
				// 当前选中tab
				tp: 0
			},
			// 上学信息
			schoolInfo: {
				// 上学信息list
				list: [],
				// 是否显示
				show: false
			},
			// 多胞胎，除主报名学生外最多再绑定2个人
			siblings: {
				// 所有双胞胎的表单
				allList: [],
				// 页面已显示的表单
				list: [],
				// 标题开关
				isHaveSib: false
			},
			// 其他补充信息
			others: {
				list: []
			},
			// 随迁
			sq: {
				// 当前报名类型是否是随迁
				isSQ: false,
				// 所有随迁子女的setupId
				idList: [12, 20, 28, 39, 47, 55, 68, 76, 84, 95, 103, 111],
				// 上次选中的类型
				lastTp: '',
				// 当前选中的类型
				tp: '',
				// 当前选中的类型
				tpCn: '',
				// 随迁子女类型时经商信息或务工信息2选1填写
				// 经商和务工
				allList: [],
				// 页面展示的类型
				list: []
			}
		}
	},
	updated() {
		this.$nextTick(() => {
			// 切换房产新tab或者切换随迁经商务工类型后
			// 直接移除所有校验，防止因为切换tab存在错误信息红字而无法提交
			if (this.$refs && this.$refs['form']) {
				this.$refs['form'].clearValidate()
			}
		})
	},
	methods: {
		// 字段通用处理：区分图片与非图片字段
		separateImgAndNormal(item) {
			// 非图片字段
			item._normalItem = item.leafFieldInfos.filter(fi => fi.type == 1)
			// 图片字段
			item._imgItem = item.leafFieldInfos.filter(fi => fi.type == 2)
			return item
		},
		//检查离异图片是否全部上传
		checkDivorced(){
			if (this.isGuardianDivorced) {
				const requiredFields = [
					{ fieldEnglish: "divorceAgreementInfo", name: "离婚协议双方信息页" },
					{ fieldEnglish: "divorceFosterInfo", name: "离婚协议抚养权信息页" },
					{ fieldEnglish: "divorceSign", name: "离婚协议签字页" }
				];

				const missingFields = [];
				const guardianInfo = this.originList.find(item => item.typeConfigId === "2");

				requiredFields.forEach(({ fieldEnglish, name }) => {
					const field = guardianInfo?._imgItem?.find(img => img.fieldEnglish === fieldEnglish);
					if (!field?.fieldValue ||
						(Array.isArray(field.fieldValue) && field.fieldValue.length === 0)) {
						missingFields.push(name);
					}
				});

				if (missingFields.length > 0) {
					this.$message.error(`监护人离异，必须上传：${missingFields.join("、")}`);
					return false;
				}
			}
			return true;
		},
		// 字段通用处理：增加验证规则
		addValidRules(item) {
			// 循环应用验证规则
			item._normalItem.forEach(fi => {
				// 直接赋值this.form会使Vue无法绑定更新，必须用$set
				// fieldEnglish会有重复，但fieldId不会
				this.$set(this.form, `${ fi.fieldId }`, fi.fieldValue)

				// 特殊处理：居住证类型字段默认值设为1（居住凭证）
				if (fi.fieldId == 700 && (!fi.fieldValue || fi.fieldValue === '')) {
					fi.fieldValue = '0'
					this.$set(this.form, `${ fi.fieldId }`, '0')
				}

				// 域为必输项
				if (fi.isNecessary == 1) {
					// 初始化规则
					let rule = {}
					if (fi.infoVerificationCode != 0) {
						// 找到对应规则
						rule = JSON.parse(JSON.stringify(rulesList[fi.infoVerificationCode]))
						// 特殊处理
						if (fi.infoVerificationCode == 4 || fi.infoVerificationCode == 8) {
							// 直接用字段名覆盖提示语
							rule.message = `请选择${ fi.fieldName }`
						} else if (fi.infoVerificationCode == 1) {
							// 身份证号
							rule.validator = idCardValidator
						} else if (fi.infoVerificationCode == 11) {
							// 手机
							rule.validator = mobileValidator
						}
					} else {
						// 必输但无格式限制
						let triggerTp = triggerTpByCode.find(v => v.id == fi.inputItemCode)
						rule = {
							required: true,
							trigger: triggerTp.val,
							message: `请${ triggerTp.valCn }${ fi.fieldName }`
						}
					}
					// 加验证规则。同上，必须用$set
					this.$set(this.rules, `${ fi.fieldId }`, rule)
				}
			})
			item._imgItem.forEach(fi => {
				this.$set(this.form, fi.fieldId, fi.fieldValue)
				if (fi.isNecessary == 1) {
					this.$set(this.rules, `${ fi.fieldId }`, rulesList[15])
				}
			})
			return item
		},
		// 字段通用处理：删除验证规则
		removeValidRules(item) {
			// 循环删除验证规则
			item._normalItem.forEach(fi => {
				this.$delete(this.form, `${ fi.fieldId }`)
				if (fi.isNecessary == 1) {
					this.$delete(this.rules, `${ fi.fieldId }`)
				}
			})
			item._imgItem.forEach(fi => {
				this.$delete(this.form, fi.fieldId)
				if (fi.isNecessary == 1) {
					this.$delete(this.rules, `${ fi.fieldId }`)
				}
			})
			return item
		},
		// 随迁类型切换
		sqTpChange(val) {
			this.sq.tp = this.sq.allList.find(v => v.infoName == val).typeConfigId
			let oldTab = this.sq.allList.filter(v => v.typeConfigId == this.sq.lastTp)
			let newTab = this.sq.allList.filter(v => v.typeConfigId == this.sq.tp)
			this.sq.list = newTab
			oldTab.map(this.removeValidRules)
			newTab.map(this.addValidRules)
			// 更新类型
			this.sq.lastTp = this.sq.tp
		},
		// 房产tab切换
		ppTabChange() {
			let oldTab = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.lastTp)
			let newTab = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
			this.propertyForm.curList = newTab
			// 删除旧tab的form字段和验证规则
			oldTab.map(this.removeValidRules)
			// 添加新tab的form字段和验证规则
			newTab.map(this.addValidRules)
			// 更新上次点击下标
			this.propertyForm.lastTp = this.propertyForm.tp
		},
		// 优抚tab切换
		ufTabChange() {
			let oldTab = this.youFuForm.list.filter(v => v.typeConfigId == this.youFuForm.lastTp)
			let newTab = this.youFuForm.list.filter(v => v.typeConfigId == this.youFuForm.tp)
			this.youFuForm.curList = newTab
			// 删除旧tab的form字段和验证规则
			oldTab.map(this.removeValidRules)
			// 添加新tab的form字段和验证规则
			newTab.map(this.addValidRules)
			// 更新上次点击下标
			this.youFuForm.lastTp = this.youFuForm.tp
		},
		// 加载或清空双胞胎
		initOrHideSib() {
			// 初始化第1个人
			if (this.siblings.isHaveSib) {
				// 页面更新
				this.siblings.list = this.siblings.allList.filter(v => v.typeConfigId == 3)
				// 再添加他的验证规则
				this.siblings.list.map(this.addValidRules)
			} else {
				// 删除所有
				this.siblings.list.map(this.removeValidRules)
				this.siblings.list = []
			}
		},
		// 双胞胎按钮 - 添加
		sibAdd() {
			// 第2个人
			let secondPeople = this.originList.filter(v => v.typeConfigId == 19)
			secondPeople.map(this.addValidRules)
			this.siblings.list.push(secondPeople[0])
		},
		// 双胞胎按钮 - 删除
		sibDel() {
			let secondPeople = this.originList.filter(v => v.typeConfigId == 19)
			secondPeople.map(this.removeValidRules)
			this.siblings.list.pop()
		},
		// 截取身份证号并更新页面
		// 身份证输入框值, 对应的生日域id, 对应的性别域id
		setBirthDtAndSexByIdCard(idCardVal, birthId, genderId) {
			if (idCardReg.test(idCardVal)) {
				// 年月日
				let birthDt = idCardVal.slice(6, 14)
				// 性别
				let gender = idCardVal[16]
				// 默认男
				let actualGender = '1'
				// 0和偶数为女
				if (gender == 0 || gender % 2 == 0) {
					actualGender = '2'
				}
				// 修改出生日期
				if (this.form.hasOwnProperty(birthId)) {
					// 防止页面初始化尚无$refs时报错
					if (this.$refs[birthId]) {
						this.$refs[birthId][0].valChange(`${birthDt.slice(0, 4)}-${birthDt.slice(4, 6)}-${birthDt.slice(6, 8)}`)
					}
				}
				// 修改性别
				if (this.form.hasOwnProperty(genderId)) {
					if (this.$refs[genderId]) {
						this.$refs[genderId][0].valChange(actualGender)
					}
				}
			}
		},
		// input值更改
		inputValueChange(v) {
			this.changeVal(v.id, v.val)
			// 基础信息 - 学生身份证号匹配
			if (v.id == '3') {
				this.setBirthDtAndSexByIdCard(v.val, '4', '5')
			}
			// 监护人信息 - 监护人1身份证号匹配
			if (v.id == '21') {
				this.setBirthDtAndSexByIdCard(v.val, '19', '17')
			}
			// 监护人信息 - 监护人2身份证号匹配
			if (v.id == '30') {
				this.setBirthDtAndSexByIdCard(v.val, '28', '26')
			}
			// 双胞胎信息 - 双胞胎1身份证号匹配
			if (v.id == '38') {
				this.setBirthDtAndSexByIdCard(v.val, '37', '35')
			}
			// 双胞胎信息 - 双胞胎2身份证号匹配
			if (v.id == '1038') {
				this.setBirthDtAndSexByIdCard(v.val, '1037', '1035')
			}
		},
		// 选择框值改变
		selectValueChange(v) {
			this.changeDivorcedVal(v.id, v.val)
			this.changeVal(v.id, v.val)

			// 毕业小学字段(fieldId: 600)特殊处理
			if (v.id == 600) {
				// 当选择"其他"时，清空毕业班级字段的值
				if (v.val == '999999') {
					this.changeVal(601, '')
				} else {
					// 当选择具体学校时，清空毕业学校字段的值
					this.changeVal(602, '')
				}
			}
			if(this.prefixDeptCode=='130209' || this.prefixDeptCode=='130204') {
				if (v.id === 7 && v.val === '1') {
					this.isDisabled = true;
					const basicInfo = this.originList.find(item => item.typeConfigId === "1");
					const basicInfoGroup = basicInfo._imgItem?.find(img => img.fieldEnglish === "reserveImg1");
					basicInfoGroup.isVisible = this.isDisabled ? '' : 'none';
					} else if (v.id === 7 && v.val === '0') {
						this.isDisabled = false;
					}

			}
			if (v.id == 350 || String(v.id) === '350') {
				this.isLuanzhouGraduate = v.val
			}
			// 处理居住证类型字段变化
			if (v.id == 700) {
				this.updateResidenceCertLabel(v.val);
			}
		},
		//单独处理离异勾选项的
		changeDivorcedVal(id, val) {
			// 假设 normalForm 是您提供的数组
			const divorcedField = this.normalForm
				.map(group => group._normalItem)  // 获取所有_normalItem数组
				.flat()                          // 合并成一个数组
				.find(item => item.fieldId === id && item.fieldEnglish === 'divorced'); // 查找fieldId为127的项
			if(divorcedField !== undefined){
				if(val === '1'){
					this.isGuardianDivorced=true;
				}else{
					this.isGuardianDivorced=false;
				}
			}
		},
		// 图片值改变
		uploadValueChange(v) {
			this.changeVal(v.id, v.val)
		},
		// 修改最终表单值，
		changeVal(id, val) {
			this.$set(this.form, id, val)
			this.originList.forEach(v => {
				v.leafFieldInfos.forEach(v1 => {
					if (v1.fieldId == id) {
						v1.fieldValue = val
					}
				})
			})
		},
		// 更新居住证编号字段标签
		updateResidenceCertLabel(residenceType) {
			// 查找居住证编号字段（fieldId 57）
			this.originList.forEach(item => {
				if (item.typeConfigId == 7) { // 居住证信息
					item._normalItem.forEach(field => {
						if (field.fieldId == 57) { // 居住证编号字段
							if (residenceType == '1') {
								field.fieldName = '居住凭证编号'
							} else {
								field.fieldName = '居住证编号'
							}
						}
					})
				}
			})
			// 强制更新视图
			this.$forceUpdate()
		},

		// 更新居住证相关字段的显示状态
		updateResidenceFieldsVisibility(residenceType) {
			this.residenceType = residenceType;

			// 如果切换为居住凭证，清空派出所和取证日期字段的值
			if (residenceType === '1') {
				this.$nextTick(() => {
					// 清空字段值
					this.$set(this.form, '143', '');
					this.$set(this.form, '144', '');

					// 更新originList中的字段值
					this.originList.forEach(item => {
						if (item.typeConfigId == 7) { // 居住证信息
							item.leafFieldInfos.forEach(field => {
								if (field.fieldId == 143 || field.fieldId == 144) {
									field.fieldValue = '';
									// 如果有DOM引用，也更新DOM值
									if (this.$refs[field.fieldId] && this.$refs[field.fieldId][0]) {
										this.$refs[field.fieldId][0].valChange('');
									}
								}
							});
						}
					});

					// 清除验证状态
					if (this.$refs.form) {
						this.$refs.form.clearValidate(['143', '144']);
					}
				});
			}
		}
	}
}
export { LoopFn }