import router from '@/router'
import NProgress from 'nprogress'
import store from '@/store'

const whiteList = ['/login', '/forgetPwd', '/404', '/weiXinTp']

NProgress.configure({showSpinner: true})

const getPageTitle = (title) => {
    if (title) return title + '-' + store.getters.siteName
    else return store.getters.siteName
};

router.beforeEach((to, from, next) => {
    NProgress.start()
    let title = String(to.meta?.title || "");
    document.title = getPageTitle(title)
    var ua = window.navigator.userAgent.toLowerCase();

    //通过正则表达式匹配ua中是否含有MicroMessenger字符串
    if (store.getters.token) {
        if (process.env.NODE_ENV == "production") {
            if (to.path === '/login') {
                next('/index')
                NProgress.done()
            } else {

                    if (store.getters.token && (to.path === '/bind-wechat' || to.path === '/bind-wechat-auth' || to.path === '/login-confirm' || to.path === '/login-confirm-auth' || to.path === '/editPwd')) {
                        next()
                    } else if (store.getters.token && store.getters.isBind === '1') {
                        if (store.getters.isDefaultPassword === false) {
                            if (store.getters.isLoginConfirm == '1') {
                                next()
                            } else {
                                router.replace("/login-confirm")
                            }
                        } else {
                            router.replace("/editPwd")
                        }
                    } else if (store.getters.token && store.getters.isBind === '0') {
                        router.replace("/bind-wechat")
                    }

            }
        } else {
            next() // 跳过绑定微信
        }
        //next() // 跳过绑定微信
    } else {
        // 没有token
        if (whiteList.indexOf(to.path) !== -1) {
            next()
        } else {
            if (ua.match(/MicroMessenger/i) == 'micromessenger') {
                next('/weiXinTp')
                NProgress.done()
            } else {
                next('/login')
                NProgress.done()
            }


        }
    }
})

router.afterEach(() => {
    NProgress.done()
})
