import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex) // 使用Vuex

const modulesFiles = require.context('./modules', true, /\.js$/)

const modules = modulesFiles.keys().reduce((modules, modulePath) => {

    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    const value = require.context('./modules', true, /\.js$/)(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {})


export default new Vuex.Store({
    plugins: [
        createPersistedState({
            key: 'shida-admin-vuex',
            storage: window.sessionStorage,
            paths: ['user', 'settings']
        })],
    modules,
    getters
})