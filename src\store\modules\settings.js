const getDefaultState = () => ({
    siteName: "唐山市教育入学一件事服务平台管理后台",
    isCollapse: false,
    openMenus: [],
    params: null
})

const state = getDefaultState()

const mutations = {
    SET_OPEN_MENUS: (state, openMenus) => {
        state.openMenus = openMenus
    },
    SET_SEARCH_PARAMS: (state, params) => {
        if (!state.params) {
            state.params = {}
        }
        state.params[params.key] = params.value
    },
    CLEAR_STATE(state) {
        state.siteName = "唐山市教育入学一件事服务平台管理后台"
        state.isCollapse = false
        state.openMenus = []
        state.params = null
    },
    REMOVE_SEARCH_PARAMS(state, key) {
        console.log('REMOVE_SEARCH_PARAMS',JSON.stringify(state.params))
        delete state.params[key]
        console.log('REMOVE_SEARCH_PARAMS',JSON.stringify(state.params))
    }
}

const actions = {
    setOpenMenus({ commit }, openMenus) {
        commit('SET_OPEN_MENUS', openMenus)
    },
    setParams({ commit }, params) {
        commit('SET_SEARCH_PARAMS', params)
    },
    removeParams({ commit }, key) {
        console.log('removeParams',key)
        commit('REMOVE_SEARCH_PARAMS', key)
    },
    clearState({ commit }) {
        commit('CLEAR_STATE')
    }

}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
