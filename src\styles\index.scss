:root {
  font-family: "微软雅黑", "Microsoft YaHei", "PingFang SC", system-ui, Avenir, Helvetica, Arial, sans-serif;
  --sd-header-background-color: #374dcc;
  --sd-sidebar-background-color: #374dcc;
  --header-height: 68px;
  --sd-sidebar-width: 240px;
  --sd-drawer-footer-height: 52px;
  --sd-primary-color: #409EFF;
  --sd-success-color: #67c23a;
  --sd-warning-color: #e6a23c;
  --sd-danger-color: #f56c6c;
  --sd-info-color: #909399;
}

body {
  margin: 0;
}

ul {
  list-style: none;
	padding: 0;
	margin: 0;
}

h1, h2, h3, h4 {
	margin: 0;
}

/* 查询条件form */
.el-form--inline .el-form-item {
	margin-bottom: 0 !important;
}

/** table内操作按钮间距 */
.el-table .el-table__cell {
  .el-link:nth-child(n+2) {
    margin-left: 5px;
  }
}

/** 操作容器 */
.sd-option-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;

  /** 按钮操作容器 */
  .sd-options {
    display: flex;
    justify-content: start;
    align-items: center;
  }

  /** 搜索容器 */
  .sd-search {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 5px;

    .el-input {
      width: 180px;
    }

    .el-select {
      width: 180px;
    }

  }
}

/** 分页容器 */
.sd-pagination-container {
  padding: 10px 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 轨道样式 */
::-webkit-scrollbar-track:hover {
  background: #f1f1f1;
  /* 轨道背景色 */
  border-radius: 8px;
  /* 滑块圆角 */
}

/* 滑块样式 */
::-webkit-scrollbar-thumb {
  background: #bebebe;
  /* 滑块颜色 */
  border: 1px solid #bebebe;
  /* 滑块边框 */
  border-radius: 8px;
  /* 滑块圆角 */
}

/* 当鼠标悬停在滑块上时改变颜色 */
::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 悬停时的颜色 */
}

/** 抽屉样式 */
.sd-drawer {

  /** 抽屉内容 */
  .sd-drawer-content {
    padding: 0 20px !important;
    height: calc(100% - var(--sd-drawer-footer-height) - 20px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  /** 抽屉底部 */
  .sd-drawer-footer {
    display: flex;
    justify-content: end;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px !important;
    height: var(--sd-drawer-footer-height);
  }
}
/** 文本颜色 */
.sd-primary-text {
  color: var(--sd-primary-color);
}

.sd-success-text {
  color: var(--sd-success-color);
}

.sd-warning-text {
  color: var(--sd-warning-color);
}

.sd-error-text {
  color: var(--sd-danger-color);
}

.sd-info-text {
  color: var(--sd-info-color);
}

.page-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10px 0;
}
// .sd-flex {
//   display: flex;
// }
// .sd-flex-center {
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// .hide .el-upload--picture-card {
//   display: none;
// }
// .hide {
// height: 146px;
// }

.normal-img-upload {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	flex-wrap: wrap;
	.key-desc {
		color: #606266;
		text-align: center;
	}
	.sample-txt {
		text-align: center;
	}
}

.normal-img-ex {
	.key-desc {
		color: #606266;
		text-align: center;
	}
	.ph {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 148px;
		height: 148px;
		color: #C0C4CC;
		background-color: #F5F7FA;
	}
}

.ad-form {
	.form-group {
		margin: 0;
		.f-group-title {
			margin-bottom: 20px;
			padding: 0 15px;
			line-height: 46px;
			border-radius: 5px;
			background-color: #F1F1F1;
		}
		.both-side {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.f-group-detail {
			margin: 0;
			margin-bottom: 30px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;
			.f-g-d-img-list {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;
			}
			.el-form-item {
				flex: 0 0 33%;
				margin-right: 0;
			}
			.normal-item {
				margin-bottom: 22px !important;
			}
			.img-item {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 0 0 14.2857%;
				margin-top: 10px;
			}
			.img-field-label {
				text-align: center;
				color: #606266;
			}
			.img-field-example	{
				text-align: center;
				font-size: 12px;
				line-height: 20px;
				color: #409eff;
			}
		}
		.f-group-tips {
			width: 100%;
			padding-top: 20px;
			padding-left: 30px;
			color: #f56c6c;
			font-size: 13px;
		}
		.sib-actions {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}

// 报名详情
.enroll-detail {
	// 字段值已修改的标红
	.is-modify {
		color: #F56C6C;
	}
}
.el-cascader-panel {

	max-height: 300px;

	/* height: 300px; */

}
// 容器flex布局居中
.b-justify-center{
	display: flex;
	justify-content: center;
	align-content: center;
}

.v-modal{
	z-index: 2000 !important;
}
.el-dialog__wrapper{
	z-index: 2001 !important;
}