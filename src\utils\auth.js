import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const IsBind = 'IsBind'
const FirstPassword = 'FirstPassword'
const LogDes='LogDes'
const Type ='Type'
const MenuName='MenuName'
const UserName='UserName'
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function setIsBind(isBind) {
  return Cookies.set(IsBind, isBind)
}
export function getIsBind() {
  return Cookies.get(IsBind)
}
export function removeIsBind() {
  return Cookies.remove(IsBind)
}
export function setFirstPassword(firstPassword) {
  return Cookies.set(FirstPassword, firstPassword)
}
export function getFirstPassword() {
  return Cookies.get(FirstPassword)
}
export function removeFirstPassword() {
  return Cookies.remove(FirstPassword)
}
export function setlogDes(logDes) {
  return Cookies.set(LogDes, logDes)
}
export function getlogDes() {
  return Cookies.get(LogDes)
}
export function removelogDes() {
  return Cookies.remove(LogDes)
}
export function setType(type) {
  return Cookies.set(Type, type)
}
export function getType() {
  return Cookies.get(Type)
}
export function removeType() {
  return Cookies.remove(Type)
}
export function setMenuName(menuName) {
  return Cookies.set(MenuName,menuName)
}
export function getMenuName() {
  return Cookies.get(MenuName)
}
export function removeMenuName() {
  return Cookies.remove(MenuName)
}
export function setUserName(userName) {
  return Cookies.set(UserName,userName)
}
export function getUserName() {
  return Cookies.get(UserName)
}
export function removeUserName() {
  return Cookies.remove(UserName)
}
