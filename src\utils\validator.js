/**
 * 电子邮箱验证
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 */
const emailValidator = (rule, value, callback) => {
    const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
    if (!value) {
        return callback(new Error("邮箱不能为空"));
    }
    if (mailReg.test(value)) {
        callback();
    } else {
        callback(new Error("请输入正确的电子邮箱格式"));
    }
}
/**
 * 验证手机号
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 * @returns 
 */
const mobileValidator = (rule, value, callback) => {
    const mobileReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
    if (mobileReg.test(value)) {
        callback();
    } else {
        callback(new Error("请输入正确格式的手机号"));
    }
}

/**
 * 验证密码
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 * @returns 
 */
const passwordValidator = (rule, value, callback) => {
    if (!value) {
        return callback(new Error("密码不能为空"));
    }
    if (value.length < 8) {
        return callback(new Error("密码长度不能小于8位"));
    }
    // 密码必须包含大小写字母、数字、特殊符号
    const passwordReg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/;
    if (!passwordReg.test(value)) {
        return callback(new Error("密码必须包含大小写字母、数字、特殊符号"));
    } else {
        callback();
    }
}

/**
 * 验证重复密码
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 */
const confirmPasswordValidator = (rule, value, callback) => {
    console.log("confirmPasswordValidator", rule)
    console.log("confirmPasswordValidator", value)
    console.log("confirmPasswordValidator", callback)
    if (!value) {
        return callback(new Error("密码不能为空"));
    }
    if (value.length < 8) {
        return callback(new Error("密码长度不能小于8位"));
    }
    // 密码必须包含大小写字母、数字、特殊符号
    const passwordReg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/;
    if (!passwordReg.test(value)) {
        return callback(new Error("密码必须包含大小写字母、数字、特殊符号"));
    } else {
        callback();
    }
    if (value !== rule.password) {
        return callback(new Error("两次输入密码不一致"));
    }
}

let idCardReg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/
/**
 * 验证身份证
 * @param {*} rule 
 * @param {*} value 
 * @param {*} callback 
 * @returns 
 */
const idCardValidator = (rule, value, callback) => {
	 let reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/
    if (reg.test(value)) {
        return callback();
    } else {
        return callback(new Error("请输入正确格式的身份证号"));
    }
}


export { 
	emailValidator, 
	mobileValidator, 
	passwordValidator, 
	confirmPasswordValidator, 
	idCardValidator,
	idCardReg
}