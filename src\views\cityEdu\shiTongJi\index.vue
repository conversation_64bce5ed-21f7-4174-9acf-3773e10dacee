<template>
  <div>
<!--    <ul class="total-num">-->
<!--      <li>-->
<!--        <h2>公办小学报名人数</h2>-->
<!--        <span>{{ tableData.studentNum }}人</span>-->
<!--      </li>-->
<!--      <li>-->
<!--        <h2>公办小学录取人数</h2>-->
<!--        <span>{{ tableData.studentNum }}人</span>-->
<!--      </li>-->
<!--      <li>-->
<!--        <h2>公办初中报名人数</h2>-->
<!--        <span>{{ tableData.studentNum }}人</span>-->
<!--      </li>-->
<!--      <li>-->
<!--        <h2>公办初中录取人数</h2>-->
<!--        <span>{{ tableData.studentNum }}人</span>-->
<!--      </li>-->
<!--      &lt;!&ndash;      <li>&ndash;&gt;-->
<!--      &lt;!&ndash;        <h2>报到人数</h2>&ndash;&gt;-->
<!--      &lt;!&ndash;        <span>{{ tableData.signNum}}人</span>&ndash;&gt;-->
<!--      &lt;!&ndash;      </li>&ndash;&gt;-->
<!--    </ul>-->
<!--      <ul class="total-num">-->
<!--        <li>-->
<!--          <h2>民办小学报名人数</h2>-->
<!--          <span>{{ tableData.studentNum }}人</span>-->
<!--        </li>-->
<!--        <li>-->
<!--          <h2>民办小学录取人数</h2>-->
<!--          <span>{{ tableData.studentNum }}人</span>-->
<!--        </li>-->
<!--        <li>-->
<!--          <h2>民办初中报名人数</h2>-->
<!--          <span>{{ tableData.studentNum }}人</span>-->
<!--        </li>-->
<!--        <li>-->
<!--          <h2>民办初中录取人数</h2>-->
<!--          <span>{{ tableData.studentNum }}人</span>-->
<!--        </li>-->
<!--        &lt;!&ndash;      <li>&ndash;&gt;-->
<!--        &lt;!&ndash;        <h2>报到人数</h2>&ndash;&gt;-->
<!--        &lt;!&ndash;        <span>{{ tableData.signNum}}人</span>&ndash;&gt;-->
<!--        &lt;!&ndash;      </li>&ndash;&gt;-->
<!--      </ul>-->
    <div class="sd-option-container">
      <div class="sd-search">
        <el-button
            size="small"
            type="primary"
            icon="el-icon-download"
            @click="downloads"
        >导出</el-button
        >
        <el-button
            size="small"
            type="primary"
            icon="el-icon-refresh"
            @click="getTableData"
        >刷新</el-button
        >
      </div>

    </div>
    <el-table
        :data="tableData"
        border
        stripe
        show-summary
        v-loading="tableLoading"
    >
      <el-table-column
          align="center"
          label="序号"
          type="index"
          width="60"
      >
        <!--        <template slot-scope="scope">-->
        <!--          <span>{{-->
        <!--              scope.$index + (search.pageNumber - 1) * search.pageSize + 1-->
        <!--            }}</span>-->
        <!--        </template>-->
      </el-table-column>
      <el-table-column
          align="center"
          label="区县名称"
          prop="deptName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="公办小学报名人数"
          prop="publicPrimaryNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="公办小学录取人数"
          prop="publicPrimaryAdmissionNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="民办小学报名人数"
          prop="privatePrimaryNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="民办小学录取人数"
          prop="privatePrimaryAdmissionNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="公办初中报名人数"
          prop="publicJuniorNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="公办初中录取人数"
          prop="publicJuniorAdmissionNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="民办初中报名人数"
          prop="privateJuniorNum"
      ></el-table-column>
      <el-table-column
          align="center"
          label="民办初中录取人数"
          prop="privateJuniorAdmissionNum"
      ></el-table-column>
    </el-table>
<!--    <div class="page-container" v-if="pageTotal > 0">-->
<!--      <el-pagination-->
<!--          background-->
<!--          @size-change="handleSizeChange"-->
<!--          @current-change="handleCurrentChange"-->
<!--          :current-page.sync="search.pageNumber"-->
<!--          layout="total, prev, pager, next, sizes"-->
<!--          :page-sizes="$pageSizes"-->
<!--          :total="pageTotal"-->
<!--      >-->
<!--      </el-pagination>-->
<!--    </div>-->
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import {pref} from "@/utils/common";
import {getTongJiList} from "@/api/shiTongJi";
export default {
  name: 'CommonHome',
  mixins: [TableMixin],
  data() {
    return {
      search: {
        keywords: "",
        nature: "",
        period: "",
      },
      tableData:[],
    };
  },
  computed: {


  },
  created() {

  },
  methods: {

    getTableData() {
      this.tableLoading = true;
      getTongJiList(this.search)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 下载
    downloads() {
      let params = this.search;
      this.$download(
          `/user-api/center/studentStatistics/exportStudent`,
          params,
          "xls",
          "统计数据列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.total-num {
  display: flex;
  border: 1px solid #ccc;
  border-radius: 4px;
  list-style: none;
  margin-bottom: 30px;
  & > li {
    height: 200px;
    flex: 0 0 25%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    & > span {
      padding-top: 40px;
    }
  }
}
</style>
