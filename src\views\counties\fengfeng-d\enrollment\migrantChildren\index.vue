<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-select
              size="small"
              v-model="search.enrollStage"
              placeholder="学段"
            >
              <el-option
                v-for="item in schoolTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.enrollId"
              placeholder="报名ID"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.studentName"
              placeholder="姓名或身份证"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.estateReviewStatus"
              placeholder="房管局审核状态"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="search.publicSecurityReviewStatus"
              placeholder="公安审核状态"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="getTableData"
            ></el-button>
          </el-form-item>
          <el-row>
            <el-form-item>
              <el-button
                size="small"
                type="primary"
                icon="el-icon-position"
                @click="assignBatch"
                >批量分配学校</el-button
              >
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      v-loading="tableLoading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="50" :fixed="true">
      </el-table-column>
      <el-table-column
        align="center"
        label="报名ID"
        prop="enrollId"
        width="140"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="studentIdCardNumber"
        width="170"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学段"
        prop="enrollStageName"
        width="85"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名时间"
        prop="enrollTime"
        width="160"
      ></el-table-column>
      <el-table-column
        align="center"
        label="房产审核"
        prop="estateReviewStatus"
      ></el-table-column>
      <el-table-column
        align="center"
        label="公安审核"
        prop="publicSecurityReviewStatus"
      ></el-table-column>
      <el-table-column
        align="center"
        label="分配学校"
        prop="adjustSchoolName"
        width="85"
      ></el-table-column>
      <el-table-column
        align="center"
        label="备注"
        prop="remark"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-position"
            type="primary"
            :underline="false"
            @click="assign(row)"
            >分配学校</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 分配学校 -->
    <el-dialog
      title="分配学校"
      :visible.sync="modal.assign"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <p class="assign-num">当前已选择人数：{{ assignForm.num }}</p>
      <el-form
        :model="assignForm"
        ref="assignForm"
        :rules="assignRules"
        label-position="right"
      >
        <el-form-item prop="schoolId" label="分配学校选择">
          <el-select
            size="small"
            v-model="assignForm.schoolId"
            placeholder="选择学校"
            clearable
            @change="schoolChange"
          >
            <el-option
              v-for="item in schoolList"
              :label="item.deptName"
              :value="item.id"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-15">
        <el-button size="small" @click="switchModal('assign', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmAssign"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { schoolTypeOptions } from "@/utils/common";
import {
  migrantChildrenPage,
  migrantChildrenAssignedSchool,
} from "@/api/enrollment";
import { getDepts } from "@/api/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      search: {
        enrollStage: '2',
        enrollId: "",
        studentName: "",
        estateReviewStatus: "",
        publicSecurityReviewStatus: "",
      },
      schoolTypeOptions: schoolTypeOptions,
      modal: {
        assign: false,
      },
      multipleSelection: [],
      schoolList: [],
      // 分配学校
      assignForm: {
        num: "",
        type: 1, //1随迁子女 2特殊群体
        schoolId: "",
        schoolName: "",
        studentId: [],
      },
      assignRules: {
        content: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  async created() {},
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      migrantChildrenPage(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: this.search.enrollStage,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 获取学校名称
    schoolChange() {
      let curSchool = this.schoolList.find(
        (item) => item.id == this.assignForm.schoolId
      );
      this.assignForm.schoolName = curSchool.deptName;
    },
    // 单条分配学校
    assign(row) {
      this.getDepts();
      this.switchModal("assign", true);
      this.$nextTick(() => {
        this.$refs.assignForm.resetFields();
        this.assignForm.schoolId = "";
        this.assignForm.schoolName = "";
        this.assignForm.num = 1;
        this.assignForm.studentId = [row.studentBaseId];
      });
    },
    // 批量分配学校
    assignBatch() {
      this.getDepts();
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      if (checkList.length > 0) {
        this.switchModal("assign", true);
        this.$nextTick(() => {
          this.$refs.assignForm.resetFields();
          this.assignForm.schoolId = "";
          this.assignForm.schoolName = "";
          this.assignForm.num = checkList.length;
          this.assignForm.studentId = checkList;
        });
      } else {
        this.$message.warning("请选择报名学生");
      }
    },
    // 分配学校 - 确定
    confirmAssign() {
      this.$refs.assignForm.validate((valid) => {
        if (valid) {
          migrantChildrenAssignedSchool(
            this.assignForm,
            this.prefixDeptCode
          ).then((res) => {
            this.switchModal("assign", false);
            this.getTableData();
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}
.assign-num {
  font-size: 16px;
}
</style>