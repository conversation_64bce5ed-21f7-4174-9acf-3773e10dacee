<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button type="primary" icon="el-icon-back" @click="back" size="small"
          >返回</el-button
        >
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus"
          >添加</el-button
        >
      </div>
    </div>
    <div class="flex-center school-name">
      {{ schoolName }}
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      style="width: 100%"
      v-loading="tableLoading"
    >
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="小学名称"
        prop="primaryName"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200px">
        <template slot-scope="{ row }">
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="del(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="初中学校">
          <span>{{ schoolName }}</span>
        </el-form-item>
        <el-form-item label="小学学校" prop="primaryIds">
          <el-select v-model="form.primaryIds" multiple size="small">
            <el-option
              v-for="item in primarySchoolList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolRelation,
  createSchoolRelation,
  deletePrimary,
} from "@/api/setting";
import { getDepts } from "@/api/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        juniorId: "",
        deptId: this.$store.getters.deptId,
        type: 2, // 1区县级别 2学校级别
      },
      form: {
        juniorId: "",
        primaryIds: [],
      },
      rules: {
        primaryIds: [
          { required: true, message: "请选择小学", trigger: "change" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
      },
      primarySchoolList: [],
      schoolName: "",
    };
  },
  created() {
    this.search.juniorId = this.$route.query.schoolId;
    this.form.juniorId = this.$route.query.schoolId;
    this.schoolName = this.$route.query.schoolName;
    this.getDeptsPrimary();
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getSchoolRelation(this.search)
        .then((res) => {
          this.tableData.records = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 获取小学
    getDeptsPrimary() {
      let params = {
        level: 3,
        period: 2,
      };
      getDepts(params).then((res) => {
        this.primarySchoolList = res;
      });
    },
    // 添加
    add() {
      this.dialogTitle = "小学对口初中设置";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    // 删除
    del(row) {
      this.$confirm(`确定删除【${row.primaryName}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        deletePrimary({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 提交
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          createSchoolRelation(this.form).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("addOrEdit", false);
            this.getTableData();
          });
        }
      });
    },
    // 返回
    back() {
      this.$router.push("/setting/juniorPrimaryMatching_ff");
    },
  },
};
</script>

<style lang="scss" scoped>
.school-name {
  font-size: 22px;
  padding-bottom: 20px;
}
</style>