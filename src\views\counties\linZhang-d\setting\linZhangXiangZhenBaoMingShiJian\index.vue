<template>
  <div>
    <div style="display: flex;justify-content: space-around">

      <shi-jian-zu-jian
          type="101"
          :beginTime="xiaoXueTime.beginTime"
          :endTime="xiaoXueTime.endTime"
          title="乡镇小学报名时间设置"
          class="zujian"
      ></shi-jian-zu-jian>

      <shi-jian-zu-jian
          type="102"
          :beginTime="xiaoXuehukouTime.beginTime"
          :endTime="xiaoXuehukouTime.endTime"
          title="小学户口报名时间设置"
          class="zujian"
      ></shi-jian-zu-jian>
    </div>
    <div style="display: flex;justify-content: space-around;margin-top: 80px">
      <shi-jian-zu-jian
          type="201"
          :beginTime="chuZhongTime.beginTime"
          :endTime="chuZhongTime.endTime"
          title="乡镇初中报名时间设置"
          class="zujian"></shi-jian-zu-jian>
      <shi-jian-zu-jian
          type="202"
          :beginTime="chuZhonghukouTime.beginTime"
          :endTime="chuZhonghukouTime.endTime"
          title="初中户口报名时间设置"
          class="zujian"></shi-jian-zu-jian>
    </div>
  </div>
</template>

<script>
import {getTimeList} from "@/api/linZhangShiJianSheZhi";
import shiJianZuJian from "@/views/countySpecial/linZhang/setting/shiJianZuJian";
export default {
  name: "index",
  components:{
    shiJianZuJian
  },
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      xiaoXueTime:{
        beginTime:'',
        endTime:''
      },
      xiaoXuehukouTime:{
        beginTime:'',
        endTime:''
      },
      chuZhongTime:{
        beginTime:'',
        endTime:''
      },
      chuZhonghukouTime:{
        beginTime:'',
        endTime:''
      },
    }
  },
  created() {
    this.getTime()
  },
  methods:{
    getTime(){
      getTimeList({key:1},this.prefixDeptCode).then(res=>{
        this.xiaoXueTime=res[0]||''
        this.xiaoXuehukouTime=res[1]||''
        this.chuZhongTime=res[2]||''
        this.chuZhonghukouTime=res[3]||''
      })
    }
  }
}
</script>

<style scoped>
.zujian{
  width:350px;
  height: 200px;
}
</style>
