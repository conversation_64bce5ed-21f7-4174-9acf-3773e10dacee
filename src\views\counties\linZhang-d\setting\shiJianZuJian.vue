<template>
  <div>
      <el-card class="box-card">
        <div slot="header">
          <span>{{title}}</span>
        </div>
        <el-form  label-width="100px">
          <el-form-item prop="beginTime" label="开始时间">
            <el-date-picker
                size="small"
                v-model="beginTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmStart({beginTime,endTime},1)"
                placeholder="请选择开始时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间">
            <el-date-picker
                size="small"
                v-model="endTime"
                type="datetime"
                :editable="false"
                :clearable="true"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="confirmEnd({beginTime,endTime},1)"
                placeholder="请选择结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <div class="flex-end">
          <el-button
              type="success"
              size="small"
              :disabled="!beginTime || !endTime"
              @click="confirmSet(1)"
          >确定</el-button
          >
        </div>
      </el-card>
  </div>
</template>

<script>
import {getBaoMingShiJianDetailUpdate,getBaoMingShiJianDetail} from "@/api/setting";
import {updateTime} from "@/api/linZhangShiJianSheZhi";
export default {
  name: "shiJianZuJian",
  props:{
    title:{
      type:String,
      default:''
    },
    beginTime: {
      type: String,
      default: ''
    },
    endTime: {
      type: String,
      default: ''
    },
    type:{
      type:String,
      default:''
    }
  },
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      yiXueXiaoBaoMingForm:{
        type:'',
        beginTime:'',
        endTime:''
      },
    }
  },
  created() {
    this.getdeTail()
  },
  methods:{
    // getdeTail(){
    //   getBaoMingShiJianDetail({"key": 666},this.prefixDeptCode).then(res=>{
    //     this.yiXueXiaoBaoMingForm=res
    //   })
    //   getBaoMingShiJianDetail({"key": 777},this.prefixDeptCode).then(res=>{
    //     this.yiZhengJianBaoMingForm=res
    //   })
    //   getBaoMingShiJianDetail({"key": 888},this.prefixDeptCode).then(res=>{
    //     this.xiuGaiBaoMingJieZhiTimeForm=res
    //   })
    // },
    confirmStart(item,index){
      if(item.beginTime&&item.endTime){
        let startTime = new Date(item.beginTime).getTime()
        let endTime = new Date(item.endTime).getTime()
        if(startTime>endTime){
          this.$message.error('开始时间不能大于结束时间')
          if(index==1){
            this.beginTime=''
          }else {
            this.beginTime=''
          }

        }
      }
    },
    confirmEnd(item,index){
      if(item.beginTime&&item.endTime){
        let startTime = new Date(item.beginTime).getTime()
        let endTime = new Date(item.endTime).getTime()
        if(startTime>endTime){
          this.$message.error('开始时间不能大于结束时间')
          if(index==1){
            this.endTime=''
          }else {
            this.endTime=''
          }
        }
      }
    },
    confirmSet(type){
       updateTime({beginTime:this.beginTime,endTime:this.endTime,type:this.type},this.prefixDeptCode).then(res=>{
          if(res){
            this.$message.success('设置时间成功')
          }
        })
    },
  }
}
</script>

<style scoped>

</style>
