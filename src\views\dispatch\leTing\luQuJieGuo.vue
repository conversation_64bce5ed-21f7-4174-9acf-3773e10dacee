<template>
  <div>
    <el-form :model="search" :inline="true">
      <el-form-item>
        <el-input v-model="search.keywords" size="small" clearable placeholder="学生姓名/身份证号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select clearable size="small" v-model="search.schoolId" placeholder="派位成功学校"  @change="change" :filterable="true">
          <el-option
              v-for="item in schoolList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
            @change="change"
            size="small"
            v-model="search.sex"
            placeholder="选择性别"
            class="sd-w-130"
            clearable
        >
          <el-option label="男" :value="1"></el-option>
          <el-option label="女" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
            size="small"
            type="primary"
            icon="el-icon-search"
            @click="getTableData1"
        ></el-button>
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportData"
      >导出报名信息</el-button
      >
        <el-button  v-if="prefixDeptCode === '130225'"
                    size="small"
                    type="warning"
                    @click="resetPw"
        >重置派位</el-button
        >
      </el-form-item>

    </el-form>
    <el-table :data="tableData" border stripe >
			<el-table-column
				type="index"
				align="center"
				label="序号"
				width="100"
			></el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
      ></el-table-column>
      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
      ></el-table-column>
      <el-table-column
          align="center"
          label="性别"
          prop="studentIdCardNumber"
      >
        <template slot-scope="scope">
          {{ scope.row.studentGender == 1 ? "男" :scope.row.studentGender == 0 ? "女" :''}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="双胞胎信息"
          width="100"
      >
        <template slot-scope="scope">
          <el-tooltip v-if="getTwinInfo(scope.row) !== '-'" :content="getTwinTooltipContent(scope.row)" placement="top">
            <span class="twin-info-cell">{{ getTwinInfoDisplay(scope.row) }}</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="录取学校"
          prop="volunteerSchoolName"
      ></el-table-column>

    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :total="total"
          :page-sizes="pageSizes"
      >
        <!--        :page-sizes="$pageSizes"-->
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getPWSchoolList, huoQuChengGongPaiWei, resetPw} from "@/api/quXianPW";
import { getDepts } from "@/api/common"
import {pref} from "@/utils/common";
export default {
  name: "luQuJieGuo",
  data(){
    return{
      prefixDeptCode: this.$store.getters.deptCode,
      tableData:[],
      schoolList:[],
      pageSizes:[10,300,500,800],
      total:0,
      search:{
        pageNumber:1,
        pageSize:10,
				isTwins: ''
      }
    }
  },
  created() {
    this.getSchool()
		this.getTableData()
  },
  methods:{
    resetPw(){
      //重置派位结果
      resetPw({schoolId: this.assingnmentSchoolId, step:this.agmtSchool},this.prefixDeptCode).then(res=>{
        this.$message.success('重置成功')
      })
    },
    change(){
      this.$forceUpdate()
    },
    getTableData1(){
     this.search.pageNumber=1
     this.getTableData()
    },
    getTableData(){
      huoQuChengGongPaiWei(this.search,this.prefixDeptCode).then(res=>{
        this.tableData = res.records
        this.total =Number(res.total)
      })
    },
    getSchool(){
      getDepts({
				keywords: '',
				nature: '',
				period: 3,
				parentId: 176,
				type: '',
				rejectSchoolId: '',
				level: 3,
				status: 1
			}, this.prefixDeptCode).then(res=>{
        this.schoolList = res
      })
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getTableData()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getTableData()
    },
    exportData(){
			this.$message.info('数据量较大，请耐心等待')
      let params = this.search;
      this.$download(
          `${pref+this.prefixDeptCode}/biz/TbPaiWeiSchoolSettingController/exportPwData`,
          params,
          "xls",
          "派位成功学生列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    // 获取双胞胎信息
    getTwinInfo(row) {
      // 尝试多种可能的双胞胎字段名
      const twinFields = [
        'twinInfo', 'twinCode', 'shuangBaoTaiInfo', 'shuangBaoTai',
        'twinNumber', 'twinId', 'siblingInfo', 'siblingCode',
        'doubleInfo', 'doubleCode', 'twins'
      ];

      for (let field of twinFields) {
        if (row[field] && typeof row[field] === 'string' && row[field].trim() !== '' && row[field].trim() !== '否') {
          // 返回双胞胎编号
          return row[field].trim();
        }
      }

      // 如果没有找到双胞胎信息，返回空
      return "-";
    },

    // 获取双胞胎信息显示文本（带省略号）
    getTwinInfoDisplay(row) {
      const twinInfo = this.getTwinInfo(row);
      if (twinInfo === '-') {
        return '-';
      }

      // 如果文本过长，显示省略号
      const maxLength = 10; // 最大显示字符数
      if (twinInfo.length > maxLength) {
        return twinInfo.substring(0, maxLength) + '...';
      }

      return twinInfo;
    },
    // 生成双胞胎悬浮提示内容
    getTwinTooltipContent(row) {
      const twinInfo = this.getTwinInfo(row);
      if (twinInfo === '-') {
        return '';
      }

      // 从双胞胎编号中提取身份证号码
      // 格式如：是(理想-******************,你好-******************)
      const idCards = [];

      // 使用正则表达式提取身份证号码（18位数字或17位数字+X）
      const idCardRegex = /(\d{17}[\dX])/g;
      let match;
      while ((match = idCardRegex.exec(twinInfo)) !== null) {
        idCards.push(match[1]);
      }

      if (idCards.length > 0) {
        return idCards.join('\n');
      } else {
        return '--';
      }
    },
  }
}
</script>

<style scoped>
.twin-info-cell {
  color: #409EFF;
  cursor: pointer;
}
</style>
