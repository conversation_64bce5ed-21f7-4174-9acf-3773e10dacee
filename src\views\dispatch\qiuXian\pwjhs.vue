<template>
  <div>
    <el-table
        :data="tableData"
        style="width: 100%"
        border
    >
      <el-table-column
          prop="schoolName"
          label="学校名称"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="numOne"
          label="男生计划数"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="numTwo"
          label="女生计划数"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="address"
          label="总计划数"
          align="center"
      >
        <template slot-scope="scope">
          <span>{{scope.row.numOne!=null&&scope.row.numOne!=null?Number(scope.row.numOne)+Number(scope.row.numTwo):'' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              @click="jiHua(scope.row)"
          >设置计划数</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
        title="设置招生计划数"
        :visible.sync="jiHuaShow"
        align="center"
    >
      <el-form :model="form" style="width:300px">
        <el-form-item
            label="男生计划数"
        >
          <el-input
              v-model="form.numOne"
              placeholder="请输入男生计划数"
              clearable
          ></el-input>
        </el-form-item>
        <el-form-item
            label="女生计划数"
        >
          <el-input
              v-model="form.numTwo"
              placeholder="请输入女生计划数"
              clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="计划总数">
          <span style="color: red;font-size:25px;font-weight: bold">  {{Number(form.numOne) +Number(form.numTwo) }}</span>
        </el-form-item>
      </el-form>
      <span style="color: red;font-size:25px;font-weight: bold">
            注: 请确认计划总数是否符合学校学位数，如不符合请调整男女生计划数 (派位系统以本次设置的男女生计划数进行派位，
            计划总数与学校学位数不符请于开始派位前取消进行调整，开始派位将无法挽回派位结果)
          </span>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="jiHuaShow = false">取 消</el-button>
        <el-button
            type="primary"
            @click="add"
        >确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getPWSchoolList,updateSchoolaJiHuaShu} from "@/api/quXianPW";

export default {
  name: "pwjhs",
  data(){
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      jiHuaShow:false,
      form: {
        numOne: '',
        numTwo: '',
      },
      tableData:[]
    }
  },
  created() {
    this.getTable()
  },
  methods:{
    getTable(){
      getPWSchoolList({},this.prefixDeptCode).then(res=>{
        this.tableData=res
      })
    },
    jiHua(row){
      this.form.schoolId=row.schoolId
      this.form.numOne=row.numOne
      this.form.numTwo=row.numTwo
      this.form.id=row.id
      this.jiHuaShow=true
    },
    add(){
      updateSchoolaJiHuaShu(this.form,this.prefixDeptCode).then(res=>{

          this.$message.success('计划数设置成功')
          this.jiHuaShow=false
          this.getTable()
      })
      // this.jiHuaShow=false
    }
  }
}
</script>

<style scoped>

</style>
