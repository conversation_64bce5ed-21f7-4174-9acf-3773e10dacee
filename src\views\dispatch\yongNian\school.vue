<template>
	<div class="dispatch-school-list">
		<el-table :data="list" border stripe style="width: 100%">
			<el-table-column
				align="center"
				label="序号"
				width="60"
				type="index"
			></el-table-column>
			<el-table-column
			  align="center"
			  label="学校名称"
			  prop="schoolName"
			></el-table-column>
			<el-table-column
			  align="center"
			  label="招生计划数"
			  prop="planNum"
			></el-table-column>
			<el-table-column
			  align="center"
			  label="报名人数"
			  prop="enrollNum"
			></el-table-column>
			<el-table-column
			  align="center"
			  label="状态"
			  prop="status"
			>
				<template slot-scope="{ row }">
					{{ statusMap[ row.status ] }}
				</template>
			</el-table-column>
			<el-table-column
			  align="center"
			  label="操作"
			>
				<template slot-scope="{ row }">
					<el-button size="small" type="primary" v-if="row.status == 1 || row.status == 2" @click="go2Dispatch(row)">开始随机派位</el-button>
					<el-button size="small" type="success" v-else-if="row.status == 3 || row.status == 4" @click="go2Detail(row)">查看派位结果</el-button>
					<el-button size="small" plain type="success" :disabled="row.isSync == 1" @click="syncResult(row)">同步派位结果</el-button>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
import { pwSchoolList, syncPwResult } from '@/api/yongNianPw.js'
export default {
	data() {
		return {
			prefixDeptCode: this.$store.getters.deptCode,
			list: [],
			statusMap: ['', '待派位', '进行派位', '派位结束', '无需派位']
		}
	},
	created() {
		sessionStorage.removeItem('ynPWSchool')
		this.getList()
	},
	methods: {
		// 待派位学校
		getList() {
			pwSchoolList({}, this.prefixDeptCode).then(res => {
				this.list = res
			})
		},
		// 前往派位
		go2Dispatch(row) {
			sessionStorage.setItem('ynPWSchool', JSON.stringify(row))
			this.$router.push('/dispatch/pwOperateYN')
		},
		// 查看结果
		go2Detail(row) {
			sessionStorage.setItem('ynPWSchool', JSON.stringify(row))
			this.$router.push('/dispatch/pwResultYN')
		},
		// 同步结果
		syncResult(row) {
			this.$confirm(`同步${ row.schoolName }的派位结果？`, '提示', {
				type: 'warning'
			}).then(() => {
				syncPwResult({
					key: row.id
				}, this.prefixDeptCode).then(res => {
					this.$message.success(`${ row.schoolName }的派位结果已同步`)
					this.getList()
				})
			}).catch(() => {})
		}
	}
}
</script>

<style>
</style>