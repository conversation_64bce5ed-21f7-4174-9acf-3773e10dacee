<template>
  <div>
    <div class="section ad-form">
      <el-skeleton
          :loading="loadingAdForm"
          animated
      >
        <template slot="default">
          <el-alert
              style="margin-bottom: 10px;"
              type="success"
              effect="dark"
              :title="`当前报名学校：${ adSchool.deptName }`"
              :closable="false">
          </el-alert>
          <dl class="form-group">
            <dt class="f-group-title">报名类别</dt>
            <dd class="f-group-detail">
              <el-button size="small" type="primary" @click="openTpSelect">修改报名类别</el-button>
            </dd>
          </dl>
          <el-form ref='form' :model="form" :rules="rules" size="small" :inline="true" label-width="170px">
            <!-- 普通字段 -->
            <dl v-for="item, idx in normalForm" :key="item.typeConfigId" class="form-group">
              <dt class="f-group-title">{{ item.infoName }}</dt>
              <dd class="f-group-detail">
                <div>
                  <template v-for="fi, fidx in item._normalItem">
                    <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                      <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                    v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                      <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select"
                                     @value-change="selectValueChange"
                                     v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                    </el-form-item>
                  </template>
                </div>
                <template v-for="fi, fidx in item._imgItem">
                  <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                    <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                       @value-change="uploadValueChange"></normal-img-upload>
                  </el-form-item>
                </template>
                <div class="f-group-tips" v-if="item._imgItem.length > 0">
                  <div class="tips-item" v-if="item.typeConfigId == 2">
                    <p>注：</p>
                    <ol>
                      <li>含*项均为必填项，请核查后仔细填写，确保无误</li>
                      <li>上传的照片大小应小于6M</li>
                    </ol>
                  </div>
                </div>
              </dd>
            </dl>
            <!-- 双胞胎 -->
            <dl class="form-group" v-if="siblings.allList.length > 0">
              <dt class="f-group-title">
                <el-checkbox v-model="siblings.isHaveSib" @change="initOrHideSib">双胞胎信息</el-checkbox>
              </dt>
              <dd class="f-group-detail" v-show="siblings.isHaveSib">
                <ul class="sib-list">
                  <li class="sib-item" v-for="si, sIdx in siblings.list" :key="si.typeConfigId">
                    <el-divider content-position="left">双胞胎{{ sIdx + 1 }}</el-divider>
                    <div>
                      <template v-for="fi, fidx in si._normalItem">
                        <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                          <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                        v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                          <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select"
                                         @value-change="selectValueChange"
                                         v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                        </el-form-item>
                      </template>
                    </div>
                    <div class="f-g-d-img-list">
                      <template v-for="fi, fidx in si._imgItem">
                        <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                          <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                             @value-change="uploadValueChange"></normal-img-upload>
                        </el-form-item>
                      </template>
                    </div>
                  </li>
                </ul>
                <div class="sib-actions">
                  <el-button size="small" type="primary" @click="sibAdd" v-if="siblings.list.length == 1">添加
                  </el-button>
                  <el-button size="small" type="danger" @click="sibDel" v-if="siblings.list.length == 2">删除
                  </el-button>
                </div>
              </dd>
            </dl>
            <!-- 随迁 -->
            <dl class="form-group" v-if="sq.isSQ">
              <dt class="f-group-title both-side">
                <span>{{ sq.tpCn }}</span>
                <el-radio-group v-model="sq.tpCn" @input="sqTpChange" size="mini">
                  <el-radio-button :key="sqRadioItem.typeConfigId" v-for="sqRadioItem in sq.allList"
                                   :label="sqRadioItem.infoName"></el-radio-button>
                </el-radio-group>
              </dt>
              <dd class="f-group-detail">
                <div v-for="sqItem in sq.list" :key="sqItem.typeConfigId">
                  <div>
                    <template v-for="fi, fidx in sqItem._normalItem">
                      <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                        <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                      v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                        <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select"
                                       @value-change="selectValueChange"
                                       v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                      </el-form-item>
                    </template>
                  </div>
                  <div class="f-g-d-img-list">
                    <template v-for="fi, fidx in sqItem._imgItem">
                      <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                        <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                           @value-change="uploadValueChange"></normal-img-upload>
                      </el-form-item>
                    </template>
                  </div>
                </div>
              </dd>
            </dl>
            <!-- 房产 -->
            <dl class="form-group" v-if="propertyForm.list.length > 0">
              <dt class="f-group-title both-side">
                <span>房产信息</span>
                <el-select style="width: 200px" size="small" v-model="propertyForm.tp" @change="ppTabChange">
                  <el-option v-for="ppItem in propertyForm.list" :value="ppItem.typeConfigId" :label="ppItem.infoName"
                             :key="ppItem.typeConfigId"></el-option>
                </el-select>
              </dt>
              <dd class="f-group-detail">
                <div v-for="ppItem in propertyForm.curList" :key="ppItem.typeConfigId">
                  <div>
                    <template v-for="fi, fidx in ppItem._normalItem">
                      <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                        <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                      v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                        <normal-select :ref="fi.fieldId" :item-config.sync="fi" :extra-config="schoolParams4Select"
                                       @value-change="selectValueChange"
                                       v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                      </el-form-item>
                    </template>
                  </div>
                  <div class="f-g-d-img-list">
                    <template v-for="fi, fidx in ppItem._imgItem">
                      <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                        <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                           @value-change="uploadValueChange"></normal-img-upload>
                      </el-form-item>
                    </template>
                  </div>
                </div>
              </dd>
            </dl>
            <template v-if="others.list.length > 0">
              <dl v-for="oItem, oIdx in others.list" :key="oItem.typeConfigId" class="form-group">
                <dt class="f-group-title" v-if="prefixDeptCode == '130481'">乡镇购房材料</dt><!-- 武安 -->
                <dt class="f-group-title" v-else>其他材料证明</dt>
                <dd class="f-group-detail">
                  <div>
                    <template v-for="(fi, fidx) in oItem._normalItem">
                      <el-form-item :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
                        <normal-input :ref="fi.fieldId" :item-config.sync="fi" @value-change="inputValueChange"
                                      v-if="fi.inputItemCode == 1 || fi.inputItemCode == 4"></normal-input>
                        <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                                       v-else-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6"></normal-select>
                      </el-form-item>
                    </template>
                  </div>
                  <template v-for="fi, fidx in oItem._imgItem">
                    <el-form-item :prop="`${ fi.fieldId }`" class="f-g-d-item img-item">
                      <normal-img-upload :ref="fi.fieldId" :item-config.sync="fi"
                                         @value-change="uploadValueChange"></normal-img-upload>
                    </el-form-item>
                  </template>
                </dd>
              </dl>
            </template>


            <!-- 随迁居住证信息 -->
            <dl class="form-group" v-if="liveInfo.liveType">
              <dt class="f-group-title both-side">
                <span>居住证信息</span>
              </dt>
              <dd class="f-group-detail">
                <el-form-item label="居住证类型">
                  <el-input :value="liveInfo.liveType === 0 ? `唐山各区户籍` : `唐山各县及外省市户籍`" disabled></el-input>
                </el-form-item>
              </dd>
              <dd class="f-group-detail" v-if="liveInfo.liveType==='1'">
                <template>
                  <el-form-item label="居住证编号">
                    <el-input v-model="liveInfo.liveOption.code" disabled></el-input>
                  </el-form-item>
                  <el-form-item label="居住证地址">
                    <el-input v-model="liveInfo.liveOption.address" disabled></el-input>
                  </el-form-item>
                  <el-form-item label="居住证持有者">
                    <el-input v-model="liveInfo.liveOption.holder" disabled></el-input>
                  </el-form-item>
                  <el-form-item label="持有者身份证号">
                    <el-input v-model="liveInfo.liveOption.idCard" disabled></el-input>
                  </el-form-item>
                  <el-form-item label="与学生关系">
                    <el-input v-model="liveInfo.liveOption.relation" disabled></el-input>
                  </el-form-item>
                </template>

              </dd>
              <dd class="f-group-detail">
                <el-form-item label="居住凭证">
                  <normal-img-ex v-if="liveInfo.liveOption.liveCert" :item-config="{'fieldId':'liveCert','fieldValue':liveInfo.liveOption.liveCert}"></normal-img-ex>
                </el-form-item>
              </dd>
            </dl>

          </el-form>
          <div style="display: flex; justify-content: center; align-items: center;">
            <el-button @click="_gck()">返回</el-button>
            <el-button type="primary" @click="submitAdForm">提交</el-button>
          </div>
        </template>
      </el-skeleton>
      <el-dialog :visible.sync="modal.tpAndSchoolSelect" title="报名类别与学校选择" width="500px" center
                 :close-on-click-modal="false" :show-close="false">
        <el-form ref="tpAndSchoolForm" :model="tpAndSchoolForm" :rules="tpFormRules" label-width="120px">
          <el-form-item prop="entry1" label="报名入口：" v-if="curRole == 'COUNTY_ADMIN'">
            <el-select size="small" placeholder="请选择报名入口" v-model="tpAndSchoolForm.entry1" @change="isInTime1">
              <el-option v-for="en1 in entry1List" :key="en1.id" :label="en1.name" :value="en1.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="entry2" label="报名学段：" v-if="curRole == 'COUNTY_ADMIN'">
            <!-- <el-select :disabled="true" size="small" placeholder="请选择报名学段" v-model="tpAndSchoolForm.entry2" @change="isInTime1" v-loading="entry2Load" element-loading-spinner="el-icon-loading">
              <el-option v-for="en2 in entry2List" :key="en2.id" :label="en2.name" :value="en2.id"></el-option>
            </el-select> -->
            <span v-if="tpAndSchoolForm.entry2 == 7 || tpAndSchoolForm.entry2 == 63">小学</span>
            <span v-else-if="tpAndSchoolForm.entry2 == 8 || tpAndSchoolForm.entry2 == 64">初中</span>
            <span v-else>请选择报名入口</span>
          </el-form-item>
          <el-form-item prop="entry3" label="报名类别：">
            <el-select size="small" placeholder="请选择报名类别" v-model="tpAndSchoolForm.entry3" v-loading="entry3Load"
                       element-loading-spinner="el-icon-loading">
              <el-option v-for="en3 in entry3List" :key="en3.setupId" :label="en3.idsName"
                         :value="en3.setupId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="school" label="报名学校：" v-if="curRole == 'COUNTY_ADMIN'">
            <el-select filterable size="small" placeholder="请选择报名学校" v-model="tpAndSchoolForm.school"
                       v-loading="adSchoolLoad" element-loading-spinner="el-icon-loading">
              <el-option v-for="sc in adSchoolList" :key="sc.id" :label="sc.deptName" :value="sc.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button size="small" @click="cancelModal">取消</el-button>
          <el-button size="small" type="primary" @click="confirmAndQryForm">确定</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 随迁类型输入框 -->
    <el-dialog :visible.sync="liveDialog">
      <live @nextAdForm="nextAdForm"></live>
    </el-dialog>
  </div>

</template>

<script>
import {addAd, getEntryBySchool, getSetupSaveDetail, getSetupSaveIds, qryAdFormByEntry} from "@/api/enrollment.js"
import {schoolList} from "@/api/setting.js"
import NormalInput from "@/components/Form/NormalInput"
import NormalSelect from "@/components/Form/NormalSelect"
import NormalImgUpload from "@/components/Form/NormalImgUpload"
import {LoopFn} from "@/mixins/loopFn"
import ModalMixin from "@/mixins/ModalMixin"
import {identifyTypeEnum} from "@/utils/common";
import live from "@/views/enrollment/live/index.vue";
import normalImgEx from "@/components/Exhibition/NormalImgEx.vue";

export default {
  components: {
    NormalInput,
    NormalSelect,
    NormalImgUpload,
    live,
    normalImgEx
  },
  mixins: [LoopFn, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      curRole: this.$store.state.user.role,
      // 报名类别1列表
      entry1List: [],
      // 报名类别2列表
      entry2List: [],
      entry2Load: false,
      // 报名类别3列表
      entry3List: [],
      entry3Load: false,
      // 要报名的学校列表
      adSchoolList: [],
      adSchoolLoad: false,
      liveDialog: false,
      // 类别和学校弹窗form
      tpAndSchoolForm: {
        entry1: '',
        entry2: '',
        entry3: '',
        school: '',
      },
      liveInfo: {},
      identifyTypeEnum: identifyTypeEnum,
      tpFormRules: {
        entry3: [{required: true, message: '请选择报名类别', trigger: 'change'}],
        school: [{required: true, message: '请选择报名学校', trigger: 'change'}]
      },
      // select表单域用到的额外参数
      schoolParams4Select: {
        deptCode: '',
        nature: ''
      },
      // 已选的报名学校
      adSchool: {
        deptName: '',
        id: ''
      },
      modal: {
        tpAndSchoolSelect: true
      }
    }
  },
  async created() {
    // 如果学校，报名学校即当前学校
    if (this.curRole == 'SCHOOL') {
      let storage = this.$store.state.user
      this.adSchool.id = storage.userInfo.deptId
      this.adSchool.deptName = storage.userInfo.deptName
      this.getEntryTp1BySchool(storage.userInfo.deptId)
    } else if (this.curRole == 'COUNTY_ADMIN') {
      // 区教育局
      this.getEntryTp1()
    }
  },
  methods: {
    // 入口 - 学校管理员
    getEntryTp1BySchool(id) {
      this.tpAndSchoolForm.entry3 = ''
      this.entry3Load = true
      getEntryBySchool({
        key: id
      }, this.prefixDeptCode).then(res => {
        this.entry3List = res
        this.entry3Load = false
      }).catch(() => {
        this.entry3Load = false
      })
    },
    // 入口
    getEntryTp1() {
      getSetupSaveIds({key: 1}, this.prefixDeptCode).then(res => {
        this.entry1List = res
      })
    },
    // 学段
    getEntryTp2(k) {
      let tp = this.$route.query.period
      if (k == 2) {
        if (tp == 'primary') {
          this.tpAndSchoolForm.entry2 = '7'
        } else if (tp == 'junior') {
          this.tpAndSchoolForm.entry2 = '8'
        }
      } else if (k == 3) {
        if (tp == 'primary') {
          this.tpAndSchoolForm.entry2 = '63'
        } else if (tp == 'junior') {
          this.tpAndSchoolForm.entry2 = '64'
        }
      }
      this.isInTime1(this.tpAndSchoolForm.entry2)
      // this.getEntryTp3(this.tpAndSchoolForm.entry2)
      // this.entry2Load = false
      /* getSetupSaveIds({ key: k }, this.prefixDeptCode).then((res) => {
        this.entry2List = res
        this.entry2Load = false
      }).catch(err => {
        this.entry2Load = false
      }) */
    },
    // 类别
    getEntryTp3(k) {
      this.tpAndSchoolForm.entry3 = ''
      this.entry3Load = true
      getSetupSaveDetail({key: k}, this.prefixDeptCode).then((res) => {
        this.entry3List = res
        this.entry3Load = false
      }).catch(() => {
        this.entry3Load = false
      })
    },
    // 获取学校
    getSchoolList() {
      this.tpAndSchoolForm.school = ''
      this.adSchoolLoad = true
      let params = {
        keywords: "",
        nature: this.tpAndSchoolForm.entry1 - 1,
        period: "",
        deptCode: this.prefixDeptCode,
        type: 1,
        rejectSchoolId: '',
        pageNumber: 1,
        pageSize: 9999
      }
      let entry2 = this.tpAndSchoolForm.entry2
      // 城区幼儿园
      if (entry2 == 62) {
        params.period = 1
      } else if (entry2 == 7 || entry2 == 63) {
        // 7乡镇小学，63城区小学
        params.period = 2
      } else if (entry2 == 8 || entry2 == 64) {
        // 8乡镇初中，64城区初中
        params.period = 3
      }
      schoolList(params).then(res => {
        this.adSchoolList = res.records
        this.adSchoolLoad = false
      }).catch(() => {
        this.adSchoolLoad = false
      })
    },
    // 报名入口是否报名时间内1
    isInTime1(curTp) {
      if (curTp == 2 || curTp == 3) {
        this.getEntryTp2(curTp)
      } else if (curTp == 7 || curTp == 8 || curTp == 63 || curTp == 64) {
        this.getEntryTp3(curTp)
        if (this.curRole == 'COUNTY_ADMIN') {
          this.getSchoolList()
        }
      }
    },
    // 打开弹窗
    openTpSelect() {
      // this.tpAndSchoolForm.entry2 = ''
      this.tpAndSchoolForm.entry3 = ''
      this.tpAndSchoolForm.school = ''
      // this.entry2List = []
      this.adSchoolList = []
      if (this.curRole == 'COUNTY_ADMIN') {
        this.entry3List = []
        this.getEntryTp2(this.tpAndSchoolForm.entry1)
      }
      this.switchModal("tpAndSchoolSelect", true)
    },
    // 取消弹窗
    cancelModal() {
      // 如果已加载了表单，仅关闭弹窗
      if (this.originList.length > 0) {
        this.switchModal("tpAndSchoolSelect", false)
      } else {
        // 否则返回
        this.$router.go(-1)
      }
    },
    // 确定关闭弹窗并加载报名表单
    confirmAndQryForm() {
      this.$refs['tpAndSchoolForm'].validate((valid) => {
        if (valid) {
          this.switchModal("tpAndSchoolSelect", false)
          // 如果区县管理员
          if (this.curRole == 'COUNTY_ADMIN') {
            this.adSchool.deptName = this.adSchoolList.find(v => v.id == this.tpAndSchoolForm.school).deptName
            this.adSchool.id = this.tpAndSchoolForm.school
            this.schoolParams4Select.schoolId = this.tpAndSchoolForm.school
          } else {
            this.schoolParams4Select.schoolId = this.adSchool.id
          }
          // 是否随迁类型
          this.sq.isSQ = this.sq.idList.some(v => v == this.tpAndSchoolForm.entry3)
          // 更新额外参数
          this.schoolParams4Select.deptCode = this.prefixDeptCode
          this.schoolParams4Select.nature = this.tpAndSchoolForm.entry1 - 1
          if (this.sq.isSQ) {
            // 打开户籍弹窗
            this.liveDialog = true;
          } else {
            this.loadingAdForm = true
            this.getAdForm()
          }
        } else {
          return false
        }
      })
    },
    nextAdForm(liveInfo) {
      this.liveInfo = liveInfo;
      this.liveDialog = false;
      this.loadingAdForm = true
      this.getAdForm()
    },
    // 获取报名字段
    getAdForm() {
      qryAdFormByEntry({
        key: this.tpAndSchoolForm.entry3
      }, this.prefixDeptCode).then(res => {
        // 按typeConfigId从小到大排序
        let resCopy = JSON.parse(JSON.stringify(res)).sort((a, b) => a.typeConfigId - b.typeConfigId)
        // 分类
        this.originList = resCopy.map(this.separateImgAndNormal)
        // 是随迁
        if (this.sq.isSQ) {
          // 排除经商5和务工6
          let normalIdList = [1, 2, 4, 7].map(v => `${v}`)
          this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
          this.sq.allList = this.originList.filter(v => v.typeConfigId == 5 || v.typeConfigId == 6)
          // 如果配置了经商务工
          if (this.sq.allList.length > 0) {
            this.sq.tp = this.sq.allList[0].typeConfigId
            this.sq.tpCn = this.sq.allList[0].infoName
            this.sqTpChange(this.sq.tpCn)
          }
        } else {
          let normalIdList = [1, 2, 4, 5, 6, 7].map(v => `${v}`)
          this.normalForm = this.originList.filter(v => normalIdList.indexOf(v.typeConfigId) != -1).map(this.addValidRules)
        }
        // 房产字段：typeConfigId >= 8但小于18
        this.propertyForm.list = this.originList.filter(v => v.typeConfigId >= 8 && v.typeConfigId < 18)
        // 如果有房产
        if (this.propertyForm.list.length > 0) {
          // 房产默认选中第1个tab
          this.propertyForm.tp = this.propertyForm.list[0].typeConfigId
          this.propertyForm.lastTp = this.propertyForm.tp
          this.propertyForm.curList = this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp)
          // 添加第1个tab的验证规则
          this.propertyForm.list.filter(v => v.typeConfigId == this.propertyForm.tp).map(this.addValidRules)
        }
        // 双胞胎字段：typeConfigId为3和19
        this.siblings.allList = this.originList.filter(v => v.typeConfigId == 3 || v.typeConfigId == 19)
        // 其他补充信息
        this.others.list = this.originList.filter(v => v.typeConfigId == 18)
        if (this.others.list.length > 0) {
          this.others.list.map(this.addValidRules)
        }
        // 加载完成
        this.loadingAdForm = false
      })
    },
    // 提交
    submitAdForm() {
      // 默认第1次报名
      let params = {
        setUpSaveIds: this.tpAndSchoolForm.entry3,
        enrollSchoolId: this.adSchool.id,
        enrollSchoolName: this.adSchool.deptName,
        enrollMiddleFieldFormList: this.originList,
        houseType: this.propertyForm.tp,
        followWorkType: ''
      }
      // 没选双胞胎就去掉上传字段
      if (!this.siblings.isHaveSib) {
        params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 3 && v.typeConfigId != 19)
      } else if (this.siblings.list.length == 1) {
        // 只有一条时删掉id是19的
        params.enrollMiddleFieldFormList = params.enrollMiddleFieldFormList.filter(v => v.typeConfigId != 19)
      }
      // 是随迁传入选中的随迁类型
      if (this.sq.isSQ) {
        params.followWorkType = this.sq.tp
      }

      // 是随迁传入随迁的相关数据
      if (this.sq.isSQ) {
        params.liveInfo = {
          liveType: this.liveInfo.liveType,
          liveOption: this.liveInfo.liveOption
        };
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          addAd(params, this.prefixDeptCode).then(() => {
            this.$message.success('报名成功')
            this.$router.go(-1)
          })
        } else {
          return false
        }
      })
    },
    // 返回
    _goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.ad-form {
  background-color: #FFF;
  padding: 10px;

  .common-title {
    margin-bottom: 10px;
  }

  .ad-form-actions {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>