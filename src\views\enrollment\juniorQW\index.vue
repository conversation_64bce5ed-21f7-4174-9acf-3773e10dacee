<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
                size="small"
                v-model.trim="search.studentName"
                placeholder="姓名或身份证"
                clearable
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-date-picker
              size="small"
              v-model="search.houseDate"
              type="date"
              placeholder="购房合同时间"
              clearable
            ></el-date-picker>
          </el-form-item> -->
          <el-form-item>
            <el-date-picker
                size="small"
                v-model="search.createdTime"
                type="datetime"
                placeholder="报名开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker
                size="small"
                v-model="search.endTime"
                type="datetime"
                placeholder="报名结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select
                size="small"
                v-model="search.educationReviewStatus"
                placeholder="教育局审核状态"
                clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="驳回-修改信息" :value="3"></el-option>
              <el-option label="驳回-不可再报" :value="4"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
                size="small"
                v-model="search.graduatedSchool"
                placeholder="毕业小学"
                clearable
                filterable
            >
              <el-option :label="item.deptName" :value="item.id" v-for="item in primarySchoolList" :key="item.id"></el-option>
            </el-select>
          </el-form-item>

        
          <el-form-item>
            <el-button
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="getTableData1"
            ></el-button>
          </el-form-item>
          <el-row>
            <el-form-item>
              <el-button
                  size="small"
                  type="warning"
                  icon="el-icon-download"
                  @click="exportEnrollInfo"
                  v-if="
                  role == 'COUNTY_ADMIN' ||

                  role == 'SCHOOL'
                "
              >导出报名信息
              </el-button
              >
<!--              role == 'AUDITOR' ||-->
<!--              <el-button-->
<!--                  size="small"-->
<!--                  type="warning"-->
<!--                  icon="el-icon-download"-->
<!--                  @click="exportHouseholdSurvey"-->
<!--                  v-if="-->
<!--                  role == 'COUNTY_ADMIN' ||-->

<!--                  role == 'SCHOOL'-->
<!--                "-->
<!--              >下载入户调查单-->
<!--              </el-button-->
<!--              >-->
<!--              role == 'AUDITOR' ||-->
              <el-button
                  size="small"
                  type="success"
                  icon="el-icon-check"
                  @click="batchAudit"
                  v-if="role == 'COUNTY_ADMIN'"
              >批量审核通过
              </el-button
              >
              <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-s-promotion"
                  @click="publicAdmission"
                  v-if="showPublicAdmissionButton()"
              >公示录取结果
              </el-button
              >
<!--              <el-button-->
<!--                  size="small"-->
<!--                  type="primary"-->
<!--                  icon="el-icon-s-promotion"-->
<!--                  @click="notice"-->
<!--                  v-if="role == 'SCHOOL' && funcStatusInfo.noticeStatus"-->
<!--              >发送录取通知书-->
<!--              </el-button-->
<!--              >-->
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <el-table
        :data="tableData.records"
        border
        stripe
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
    >
      <el-table-column 
          align="center" 
          type="selection" 
          width="45" 
          :fixed="true">
      </el-table-column>
      <el-table-column
          align="center"
          label="是否公示"
          prop="publicityStatus"
          width="85"
      >
      </el-table-column>
      <el-table-column
          align="center"
          label="学生姓名"
          prop="studentName"
          min-width="90"
          :show-overflow-tooltip="true"
      >
      </el-table-column>

      <el-table-column
          align="center"
          label="身份证号"
          prop="studentIdCardNumber"
          min-width="160"
          :show-overflow-tooltip="true"
      ></el-table-column>

      <el-table-column
          align="center"
          label="毕业小学"
          min-width="160"
          :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ getSchoolName(scope.row.graduatedSchool) }}</span>
        </template>
      </el-table-column>

      <el-table-column
          align="center"
          label="意向地"
          min-width="140"
          :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ formatIntentionCity(scope.row.intentionCity) }}</span>
        </template>
      </el-table-column>

      <el-table-column
          align="center"
          label="报名时间"
          prop="enrollTime"
          min-width="140"
          :show-overflow-tooltip="true"
      ></el-table-column>

      <el-table-column
          align="center"
          label="教育局审核状态"
          min-width="100"
      >
        <template slot-scope="scope">
          <span v-if="prefixDeptCode === '130284' && isTownshipSchool(scope.row)">/</span>
          <span v-else>{{ scope.row.educationReviewStatus }}</span>
        </template>
      </el-table-column>

      <el-table-column 
          align="center" 
          label="操作" 
          min-width="280" 
          fixed="right">
        <template slot-scope="{ row, $index }">
          <el-link
              icon="el-icon-view"
              type="primary"
              :underline="false"
              style="margin-right: 8px"
              @click="detail(row, $index)"
          >详情
          </el-link>
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 8px"
              :disabled="canDisablePass(row)"
              @click="pass(row)"
              v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR') && !isDisabledForTownshipSchool(row)"
          >通过
          </el-link>
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 8px"
              :disabled="canDisableReject(row)"
              @click="reject(row)"
              v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR') && !isDisabledForTownshipSchool(row)"
          >不通过
          </el-link>
          <el-link
              icon="el-icon-check"
              type="success"
              :underline="false"
              style="margin-right: 8px"
              :disabled="canDisableSchoolPass(row)"
              @click="pass(row)"
              v-if="role == 'SCHOOL'"
          >通过
          </el-link>
          <el-link
              icon="el-icon-close"
              type="danger"
              :underline="false"
              style="margin-right: 8px"
              :disabled="canDisableSchoolReject(row)"
              @click="reject(row)"
              v-if="role == 'SCHOOL'"
          >不通过
          </el-link>
<!--          <el-link-->
<!--              icon="el-icon-refresh"-->
<!--              type="success"-->
<!--              :underline="false"-->
<!--              style="margin-right: 8px"-->
<!--              @click="adjust(row)"-->
<!--              v-if="-->
<!--              (role == 'COUNTY_ADMIN' || role == 'AUDITOR') &&-->
<!--              row.educationReviewStatus == '通过' &&-->
<!--              row.publicityStatus == '未公示' &&-->
<!--              !isDisabledForTownshipSchool(row)-->
<!--            "-->
<!--          >调剂-->
<!--          </el-link>-->
          <el-link
              icon="el-icon-remove-outline"
              type="danger"
              :underline="false"
              @click="revokePublic(row)"
              v-if="role == 'COUNTY_ADMIN' && row.publicityStatus == '已公示'"
          >撤销公示结果
          </el-link
          >
          <el-link
              icon="el-icon-delete"
              type="danger"
              :underline="false"
              @click="deleteSign(row)"
              v-if="role == 'COUNTY_ADMIN'"
          >删除报名信息
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <span class="signNum">报名总人数：{{ tableData.signNum }}</span
      >&emsp;
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="search.pageNumber"
          layout="total, prev, pager, next, sizes"
          :page-sizes="$pageSizes"
          :total="total"
      >
      </el-pagination>
    </div>

    <!-- 不通过 -->
    <el-dialog
        title="不通过"
        :visible.sync="modal.reject"
        center
        :close-on-click-modal="false"
        width="600px"
        :z-index="9999"
        append-to-body
    >
      <el-form
          :model="rejectForm"
          ref="rejectForm"
          :rules="rejectRules"
          label-position="right"
          label-width="100px"
      >
        <el-form-item prop="reviewStatus" label="驳回类型：">
          <el-select v-model="rejectForm.reviewStatus">
            <el-option label="驳回-修改信息" :value="3"></el-option>
            <el-option label="驳回-不可再报" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="reviewReason" label="驳回原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="rejectForm.reviewReason"
              placeholder="请输入驳回原因，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="tips">
        <div>注释：</div>
        <div>
          1、驳回-修改信息：选择修改信息，可以修改报名类别及基础信息，不可修改报名学校
        </div>
        <div>
          2、驳回-不可再报：选择不可再报，重新填写报名信息，不能选择原报名学校
        </div>
      </div>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('reject', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 调剂 -->
    <el-dialog
        title="调剂"
        :visible.sync="modal.adjust"
        center
        :close-on-click-modal="false"
        width="600px"
        :z-index="9999"
        append-to-body
    >
      <el-form
          :model="adjustForm"
          ref="adjustForm"
          :rules="adjustRules"
          label-position="right"
          label-width="150px"
      >
        <el-form-item label="报名ID：">{{ adjustInfo.enrollId }}</el-form-item>
        <el-form-item label="学生姓名：">{{
            adjustInfo.studentName
          }}
        </el-form-item>
        <el-form-item label="报名学校：">{{
            adjustInfo.enrollSchoolName
          }}
        </el-form-item>
        <el-form-item prop="adjustSchoolId" label="调剂学校选择：">
          <el-select
              v-model="adjustForm.adjustSchoolId"
              style="width: 300px"
              @change="adjustSchoolChange"
          >
            <el-option
                v-for="item in schoolListAvailable"
                :label="item.deptName"
                :value="item.id"
                :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('adjust', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmAdjust"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 报到 -->
    <el-dialog
        title="报到"
        :visible.sync="modal.report"
        center
        :close-on-click-modal="false"
        width="600px"
        :z-index="9999"
        append-to-body
    >
      <el-form
          :model="reportForm"
          ref="reportForm"
          :rules="reportRules"
          label-position="right"
          label-width="120px"
      >
        <el-form-item prop="reportStatus" label="报到类型：">
          <el-radio-group v-model="reportForm.reportStatus">
            <el-radio :label="1">报到</el-radio>
            <el-radio :label="2">未报到</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
            :prop="reportForm.reportStatus == 2 ? 'noShowCause' : 'empty'"
            label="未报到原因："
        >
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="reportForm.noShowCause"
              placeholder="请输入未报到原因，最多200个字符"
              style="width: 400px"
              maxlength="200"
              show-word-limit
              :disabled="reportForm.reportStatus != 2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('report', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmReport"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 优抚类型 -->
    <el-dialog
        title="优抚类型"
        :visible.sync="modal.priority"
        center
        :close-on-click-modal="false"
        width="600px"
        :z-index="9999"
        append-to-body
    >
      <el-form
          :model="priorityForm"
          ref="priorityForm"
          :rules="priorityRules"
          label-position="right"
          label-width="120px"
      >
        <el-form-item label="学生姓名：">{{
            priorityInfo.studentName
          }}
        </el-form-item>
        <el-form-item label="学生身份证号：">{{
            priorityInfo.studentIdCardNumber
          }}
        </el-form-item>
        <el-form-item prop="priorityType" label="优抚类型：">
          <el-select v-model="priorityForm.priorityType" style="width: 220px">
            <el-option
                v-for="(item, index) in priorityTypeList"
                :key="index"
                :label="item.value"
                :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            prop="priorityTypeStr"
            label="其他类型："
            v-if="priorityForm.priorityType === '其他-填写其他类型'"
        >
          <el-input
              type="input"
              v-model.trim="priorityForm.priorityTypeStr"
              placeholder="请输入其他类型"
              style="width: 220px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('priority', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmPriority"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 撤销公示结果 -->
    <el-dialog
        title="撤销公示结果"
        :visible.sync="modal.revokePublic"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="revokePublicForm"
          ref="revokePublicForm"
          :rules="revokePublicRules"
          label-position="right"
          label-width="130px"
      >
        <el-form-item prop="content" label="撤销公示原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="revokePublicForm.content"
              placeholder="请输入撤销公示原因，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('revokePublic', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmRevokePublic"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 删除报名信息 -->
    <el-dialog
        title="删除报名信息"
        :visible.sync="modal.deleteSign"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <el-form
          :model="deleteSignForm"
          ref="deleteSignForm"
          :rules="deleteSignRules"
          label-position="right"
          label-width="130px"
      >
        <el-form-item prop="content" label="删除报名原因：">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="deleteSignForm.content"
              placeholder="请输入删除报名原因，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('deleteSign', false)"
        >取消
        </el-button
        >
        <el-button size="small" type="primary" @click="confirmDeleteSign"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 发送录取通知书 -->
    <el-dialog
        title="发送录取通知书"
        :visible.sync="modal.notice"
        center
        :close-on-click-modal="false"
        width="600px"
    >
      <p class="notice-num">发送人数：{{ noticeForm.num }}</p>
      <el-form
          :model="noticeForm"
          ref="noticeForm"
          :rules="noticeRules"
          label-position="right"
      >
        <el-form-item prop="content">
          <el-input
              type="textarea"
              :rows="5"
              v-model.trim="noticeForm.content"
              placeholder="请输入录取通知书内容，最多200个字符"
              maxlength="200"
              show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-30">
        <el-button size="small" @click="switchModal('notice', false)"
        >取消
        </el-button
        >
        <el-button
            size="small"
            type="primary"
            @click="confirmNotice"
            :disabled="noticeForm.num == '' || noticeForm.num == 0"
        >确定
        </el-button
        >
      </div>
    </el-dialog>

    <!-- 详情 -->
    <el-dialog
        :visible.sync="modal.stuDetail"
        :close-on-click-modal="false"
        title="学生报名详情"
        center
        width="1240px"
        @close="stuDetailClose"
    >
      <enroll-detail
          :stu-detail="curStuDetail"
          :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <div>
        <el-skeleton :loading="loadingAuditRecord" animated>
          <p>审核情况</p>
          <el-table :data="auditRecordList" border stripe>
            <el-table-column
                align="center"
                label="操作时间"
                prop="createTime"
            ></el-table-column>
            <el-table-column
                align="center"
                label="角色"
                prop="roleName"
            ></el-table-column>
            <el-table-column
                align="center"
                label="姓名"
                prop="nickname"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作账号"
                prop="creatorName"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作记录"
                prop="content"
            ></el-table-column>
            <el-table-column
                align="center"
                label="操作"
            >
              <template slot-scope="{ row }">
                <el-button
                    type="primary"
                    plain
                    size="small"
                    v-if="row.type == 1 || row.type == 2 || row.type == 6 || row.type == 7 || row.type == 19 || row.type == 84"
                    @click="getEnrollRecords(row)"
                >报名详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-table :data="auditStatusList" border stripe>
            <el-table-column
              align="center"
              label="角色"
              prop="role"
            ></el-table-column>
            <el-table-column
              align="center"
              label="审核状态"
              prop="status"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作账号"
              prop="operationName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="驳回原因"
              prop="causeOfRejection"
            ></el-table-column>
            <el-table-column
              align="center"
              label="调剂学校"
              prop="adjustSchoolName"
            ></el-table-column>
            <el-table-column
              align="center"
              label="操作时间"
              prop="operationTime"
            ></el-table-column>
          </el-table> -->
        </el-skeleton>
      </div>
      <div class="flex-center" style="margin-top: 20px">
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            :disabled="canDisablePass(curStuDetail)"
            @click="pass(curStuDetail)"
            v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR') && !isDisabledForTownshipSchool(curStuDetail)"
        >通过
        </el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            :disabled="canDisableReject(curStuDetail)"
            @click="reject(curStuDetail)"
            v-if="(role == 'COUNTY_ADMIN' || role == 'AUDITOR') && !isDisabledForTownshipSchool(curStuDetail)"
        >不通过
        </el-button
        >
        <el-button
            icon="el-icon-check"
            type="success"
            size="small"
            :disabled="canDisableSchoolPass(curStuDetail)"
            @click="pass(curStuDetail)"
            v-if="role == 'SCHOOL'"
        >通过
        </el-button
        >
        <el-button
            icon="el-icon-close"
            type="danger"
            size="small"
            :disabled="canDisableSchoolReject(curStuDetail)"
            @click="reject(curStuDetail)"
            v-if="role == 'SCHOOL'"
        >不通过
        </el-button
        >
        <el-button type="primary" @click="prevEnrollDetail" size="small"
        >上一条
        </el-button
        >
        <el-button type="primary" @click="nextEnrollDetail" size="small"
        >下一条
        </el-button
        >
        <el-button
            icon="el-icon-refresh"
            type="warning"
            size="small"
            @click="adjust(curStuDetail)"
            v-if="
            (role == 'COUNTY_ADMIN' || role == 'AUDITOR') &&
            curStuDetail.educationReviewStatus == '通过' &&
            curStuDetail.publicityStatus == '未公示' &&
            !isDisabledForTownshipSchool(curStuDetail)
          "
        >调剂
        </el-button
        >
        <el-button size="small" type="info" @click="stuDetailClose"
        >关闭
        </el-button
        >
      </div>
    </el-dialog>
    <!-- 审核情况中指定的报名详情 -->
    <el-dialog
        :visible.sync="modal.enrollRecord"
        :close-on-click-modal="false"
        title="报名详情"
        center
        width="1240px"
    >
      <enroll-record-detail
          :key="curStuRecordId"
          :student-id="curStuRecordId"
      ></enroll-record-detail>
      <div slot="footer">
        <el-button size="small" type="info" @click='switchModal("enrollRecord", false)'>关闭</el-button>
      </div>
    </el-dialog>

    <!-- 学生直升详情 -->
    <el-dialog
        :visible.sync="modal.zhiShengDetail"
        :close-on-click-modal="false"
        title="学生直升详情"
        center
        width="550px"
    >
      <el-skeleton :loading="zhiShengDetailLoading" animated>
        <template slot="template">
          <el-skeleton-item variant="text" style="width: 50%"></el-skeleton-item>
          <el-skeleton-item variant="text" style="width: 80%"></el-skeleton-item>
          <el-skeleton-item variant="text" style="width: 60%"></el-skeleton-item>
        </template>
        <div v-if="!zhiShengDetailLoading">
          <div v-if="zhiShengDetailList.length === 0" style="text-align: center; padding: 20px; color: #999;">
            暂无学生直升信息
          </div>
          <el-table v-else :data="zhiShengDetailList" border stripe>
            <el-table-column
                align="center"
                label="学生姓名"
                prop="studentName"
                width="150"
            >
              <template slot-scope="scope">
                {{ scope.row.studentName || '-' }}
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="身份证号"
                prop="idCardNumber"
                width="200"
            >
              <template slot-scope="scope">
                {{ scope.row.idCardNumber || '-' }}
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="是否直升"
                prop="isZhiSheng"
                width="120"
            >
              <template slot-scope="scope">
                <div>
                  <el-tag :type="(scope.row.isZhiSheng === '1' || scope.row.isZhiSheng === 1) ? 'success' : 'info'">
                    {{ formatIsZhiSheng(scope.row.isZhiSheng) }}
                  </el-tag>
                  <br>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-skeleton>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="info" @click='switchModal("zhiShengDetail", false)'>关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getEnrollList,
  getTypeList,
  passAudit,
  rejectAudit,
  adjust,
  report,
  batchAudit,
  publicity,
  publicAdmissionResults,
  sendRequisition,
  updateEntitledGroup,
  getAuditRecord,
  revokePublic,
  deleteEnroll,
  isStudentZhiSheng
} from "@/api/enrollment";
import {getDepts} from "@/api/common";
import {graduateTypeEnum, pref, tangShanPriorityTypeList} from "@/utils/common";
import EnrollDetail from "@/components/EnrollDetail";
import EnrollRecordDetail from "@/components/EnrollRecordDetail";
import {getFuncStatus} from "@/api/funcSwitch";
import {getAddSignCheck} from "@/api/setting";
import areasData from "@/utils/areas.json";

export default {
  mixins: [TableMixin, ModalMixin],
  components: {EnrollDetail, EnrollRecordDetail},
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {
        enrollStage: "3",
        enrollId: "",
        studentName: "",
        schoolType: 1,
        createdTime: "",
        endTime: "",
        divorced: "",
        enrollSchoolId: "",
        adjustSchoolId: "",
        type: "",
        nature: "",
        intentionCity: [], 
        schoolReviewStatus: "",
        educationReviewStatus: "",
        estateReviewStatus: "",
        checkInStatus: "",
        admissionLetterInStatus: "",
        publicSecurityRegistrationReviewStatus: "",
        publicSecurityResidenceReviewStatus: "",
        isRelated: "",
        publicityStatus: "",
        graduateType: "",
        graduateSchool: "",
        isTransfer: 0,
        zhuJianReviewStatus: '',
        ziGuiReviewStatus: '',
        graduatedSchool:'',
        isZhiSheng: "",
        isQW: "1"  // 确保是字符串类型
      },
      typeList: [],
      modal: {
        reject: false,
        adjust: false,
        report: false,
        notice: false,
        priority: false,
        stuDetail: false,
        revokePublic: false,
        deleteSign: false,
        enrollRecord: false,
        zhiShengDetail: false
      },
      schoolList: [],
      multipleSelection: [],
      // 驳回
      rejectForm: {
        id: 0,
        reviewReason: "",
        reviewStatus: "",
      },
      rejectRules: {
        reviewStatus: [
          {
            required: true,
            message: "请选择驳回类型",
            trigger: "change",
          },
        ],
        reviewReason: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 调剂
      adjustForm: {
        id: "",
        adjustSchoolId: "",
        adjustSchoolName: "",
      },
      adjustRules: {
        adjustSchoolId: [
          {
            required: true,
            message: "请选择调剂学校",
            trigger: "change",
          },
        ],
      },
      adjustInfo: {
        enRollId: "",
        studentName: "",
        enrollSchoolName: "",
        adjustSchoolName: "",
      },
      // 报到
      reportForm: {
        id: "",
        noShowCause: "",
        reportStatus: "",
      },
      reportRules: {
        reportStatus: [
          {
            required: true,
            message: "请选择报到类型",
            trigger: "change",
          },
        ],
        noShowCause: [
          {
            required: true,
            message: "请输入未报到原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 发送录取通知书
      noticeForm: {
        content: "",
        num: "",
      },
      noticeRules: {
        content: [
          {
            required: true,
            message: "请输入录取通知书内容",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 优抚类型
      priorityForm: {
        id: "",
        priorityType: "",
        priorityTypeStr: "",
      },
      priorityRules: {
        priorityType: [
          {
            required: true,
            message: "选择优抚类型",
            trigger: "change",
          },
        ],
        priorityTypeStr: [
          {
            required: true,
            message: "输入其他类型",
            trigger: "blur",
          },
        ],
      },
      priorityInfo: {
        studentName: "",
        studentIdCardNumber: "",
      },
      priorityTypeList: tangShanPriorityTypeList,
      // 撤销公示结果
      revokePublicForm: {
        id: "",
        content: "",
      },
      revokePublicRules: {
        content: [
          {
            required: true,
            message: "请输入撤销公示原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // 删除报名信息
      deleteSignForm: {
        id: "",
        content: "",
      },
      deleteSignRules: {
        content: [
          {
            required: true,
            message: "请输入删除报名原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
      // auditStatusList: [],
      // loadingAuditStatus: true,
      auditRecordList: [],
      loadingAuditRecord: true,
      // 当前学生报名详情
      curStuDetail: {},
      // 下一条报名详情index
      index: 0,
      // 功能开关状态
      funcStatusInfo: {
        deleteStatus: false,
        publicStatus: false,
        noticeStatus: false,
      },
      // 审核情况中指定的报名详情
      curStuRecordId: '',
      graduateTypeEnum: graduateTypeEnum,
      primarySchoolList: [], // 小学列表
      // 直升详情信息
      zhiShengDetailList: [],
      zhiShengDetailLoading: false,

    };
  },
  computed: {
    schoolListAvailable() {
      return this.schoolList.filter(
          (item) =>
              item.deptName !=
              (this.adjustInfo.adjustSchoolName
                  ? this.adjustInfo.adjustSchoolName
                  : this.adjustInfo.enrollSchoolName)
      );
    },
  },
  async created() {
    // 初始化省市区数据

      this.areaOptions = areasData;


    if (this.role == "SCHOOL") {
      // 学校级 获取学校id
      this.search.enrollSchoolId = this.$store.getters.deptId;
    }
    if (this.role == "COUNTY_ADMIN" || this.role == "AUDITOR") {
      // 区县级 获取学校列表
      this.getDepts();
    }

    // 加载小学列表
    await this.loadPrimarySchools();

    // 查询报名类别
    this.getTypeList();
    // 查询功能开关状态 初中
    this.getFuncStatusInfo("DELETE_STUDENT_JUNIOR");
    this.getFuncStatusInfo("PUBLIC_STUDENT_JUNIOR");
    this.getFuncStatusInfo("NOTICE_STUDENT_JUNIOR");
  },
  methods: {
    ziGuiReviewStatusCn(val) {
      const statusMap = {
        1: '待审核',
        2: '通过',
        3: '不通过'
      };
      return statusMap[val] || '';
    },
    getTableData1() {
      this.search.pageNumber = 1;
      this.getTableData();
    },
    formatIsZhiSheng(value) {
      // 处理数字和字符串类型
      if (value === 1 || value === '1') {
        return '是';
      } else if (value === 0 || value === '0') {
        return '否';
      }
      return '-'; // 处理异常值
    },
    // 处理是否直升点击事件
    handleIsStudentZhiSheng(row) {
      this.zhiShengDetailLoading = true;
      // 调用API获取学生直升详情
      const params = {
        key: row.studentBaseId
      };

      isStudentZhiSheng(params, this.prefixDeptCode)
        .then((res) => {
          // 处理返回的数组数据
          if (res && Array.isArray(res)) {
            this.zhiShengDetailList = res;
          } else if (res && res.data && Array.isArray(res.data)) {
            this.zhiShengDetailList = res.data;
          } else {
            this.zhiShengDetailList = [];
          }
        })
        .catch((error) => {
          this.zhiShengDetailList = [];
        })
        .finally(() => {
          this.zhiShengDetailLoading = false;
        });
    },
    // 获取表格数据
    getTableData() {
      this.tableLoading = true;
      // 构建请求参数
      const params = {
        ...this.search,
        isQW: "1"  // 强制设置为字符串 "1"
      };
      
      getEnrollList(params, this.prefixDeptCode)
          .then((res) => {
            this.tableData = res;
            // 确保小学列表已加载
            if (this.primarySchoolList.length === 0) {
              this.loadPrimarySchools();
            }
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    // 获取报名类别 2小学 3初中
    getTypeList() {
      getTypeList({key: 3}, this.prefixDeptCode).then((res) => {
        this.typeList = res;
      });
    },
    // 查询学校
    getDepts() {
      let params = {
        level: 3,
        period: "3",
        type:1,
        parentId: this.$store.getters.deptId,
      };
      getDepts(params).then((res) => {
        this.schoolList = res;
      });
    },
    // 加载小学列表
    async loadPrimarySchools() {

        let primarySchoolParams = {
          level: 3,
          period: "2",
          type: 1,
        };
        const res = await getDepts(primarySchoolParams);
        this.primarySchoolList = res;

        // 强制更新视图
        this.$forceUpdate();

    },

    // 处理意向地选择变化
    handleIntentionCityChange(value) {
      this.search.intentionCity = value;
    },
    // 通过
    pass(row) {
      if (!row || !row.studentBaseId) {
        this.$message.error('获取学生信息失败，请刷新页面重试');
        return;
      }

      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // 构建参数对象
        let params = {
          id: row.studentBaseId,
          enrollStage: "3"
        };

        // 如果是滦州区学校，添加schoolType和nature参数
        const userInfo = this.$store.getters.userInfo;
        if (this.prefixDeptCode === '130284' &&
            this.role === 'SCHOOL') {
          params.schoolType = 1; // 1表示学校
          params.nature = userInfo?.deptInfo?.nature || '2'; // 传递学校性质
          console.log('传递参数:', params);
        }

        passAudit(params, this.prefixDeptCode).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        }).catch(error => {
          console.error('通过审核失败:', error);
          this.$message.error("操作失败，请重试");
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.id = row.studentBaseId;
      });
    },
    // 不通过确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {};
          // 获取用户的nature值
          const userInfo = this.$store.getters.userInfo;
          const natureValue = userInfo?.deptInfo?.nature || '2';
          
          if (this.role == "COUNTY_ADMIN" || this.role == "AUDITOR") {
            params.id = this.rejectForm.id;
            params.educationReviewStatus = this.rejectForm.reviewStatus;
            params.educationReviewReason = this.rejectForm.reviewReason;
            params.nature = natureValue; // 传递学校性质
          } else if (this.role == "SCHOOL") {
            params.id = this.rejectForm.id;
            params.schoolReviewStatus = this.rejectForm.reviewStatus;
            params.schoolReviewReason = this.rejectForm.reviewReason;
            params.nature = natureValue; // 传递学校性质
          }
          
          console.log('不通过参数:', params);
          
          rejectAudit(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("reject", false);
            this.getTableData();
          }).catch(error => {
            console.error('不通过审核失败:', error);
            this.$message.error("操作失败，请重试");
          });
        }
      });
    },
    // 调剂
    adjust(row) {
      this.switchModal("adjust", true);
      this.$nextTick(() => {
        this.$refs.adjustForm.resetFields();
        this.adjustForm.id = row.studentBaseId;
        this.adjustForm.adjustSchoolId = "";
        this.adjustForm.adjustSchoolName = "";

        this.adjustInfo.enrollId = row.enrollId;
        this.adjustInfo.studentName = row.studentName;
        this.adjustInfo.enrollSchoolName = row.enrollSchoolName;
        this.adjustInfo.adjustSchoolName = row.adjustSchoolName;
      });
    },
    // 调剂确定
    confirmAdjust() {
      this.$refs.adjustForm.validate((valid) => {
        if (valid) {
          adjust(this.adjustForm, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("adjust", false);
            this.getTableData();
          });
        }
      });
    },
    // 调剂学校选择change
    adjustSchoolChange() {
      this.schoolList.forEach((item) => {
        if (this.adjustForm.adjustSchoolId == item.id) {
          this.adjustForm.adjustSchoolName = item.deptName;
        }
      });
    },
    // 报到
    report(row) {
      this.switchModal("report", true);
      this.$nextTick(() => {
        this.$refs.reportForm.resetFields();
        this.reportForm.id = row.studentBaseId;
        this.reportForm.reportStatus = "";
        this.reportForm.noShowCause = "";
      });
    },
    // 报到确定
    confirmReport() {
      this.$refs.reportForm.validate((valid) => {
        if (valid) {
          report(this.reportForm, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("report", false);
            this.getTableData();
          });
        }
      });
    },
    // 优抚类型
    priority(row) {
      this.switchModal("priority", true);
      this.$nextTick(() => {
        this.$refs.priorityForm.resetFields();
        this.priorityForm.id = row.studentBaseId;
        this.priorityForm.entitledGroupType = "";
        this.priorityInfo.studentName = row.studentName;
        this.priorityInfo.studentIdCardNumber = row.studentIdCardNumber;
      });
    },
    // 优抚类型确定
    confirmPriority() {
      this.$refs.priorityForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.priorityForm.id,
            entitledGroupType:
                this.priorityForm.priorityType == "其他-填写其他类型"
                    ? this.priorityForm.priorityTypeStr
                    : this.priorityForm.priorityType,
          };
          updateEntitledGroup(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("priority", false);
            this.getTableData();
          });
        }
      });
    },
    // 前往添加报名信息
    go2AddAd() {
      if (this.role === "SCHOOL") {
        getAddSignCheck({key: 602}, this.prefixDeptCode).then((res) => {
          if (res) {
            // 丰南区 定制功能
            if (this.$store.getters.deptCode === "130207") {
              this.$router.push({path: "/enrollment/addFengNanAdmin", query: {period: "junior"}});
            }else if (this.$store.getters.deptCode === "130285") {
              this.$router.push({path: "/enrollment/addNanBao", query: {period: "junior"}});
            } else if (this.$store.getters.deptCode === "130271") {
              this.$router.push({path: "/enrollment/addLuTai", query: {period: "junior"}});
            }else if (this.$store.getters.deptCode === "130202") {
              this.$router.push({path: "/enrollment/addLuNan", query: {period: "junior"}});
            } else if (this.$store.getters.deptCode === "130209") {
              this.$router.push({path: "/enrollment/addCaoFeiDian", query: {period: "junior"}});
            } else {
              this.$router.push({path: "/enrollment/addAd", query: {period: "junior"}});
            }
          } else {
            this.$message.warning("添加学生报名信息时间已截止，不可添加学生报名信息，如有疑问请联系教育局");
          }
        });
      } else {
        // 古冶区 定制功能
        if (this.$store.getters.deptCode === "130204") {
          this.$router.push({path: "/enrollment/addGuyeAdmin", query: {period: "junior"},});
          // 丰南区 定制功能
        } else if (this.$store.getters.deptCode === "130207") {
          this.$router.push({path: "/enrollment/addFengNanAdmin", query: {period: "junior"},});
        } else if (this.$store.getters.deptCode === "130271") {
          this.$router.push({path: "/enrollment/addLuTai", query: {period: "junior"}});
        } else if (this.$store.getters.deptCode === "130202") {
          this.$router.push({path: "/enrollment/addLuNan", query: {period: "junior"}});
        } else if (this.$store.getters.deptCode === "130285") {
          this.$router.push({path: "/enrollment/addNanBao", query: {period: "junior"}});
        } else if (this.$store.getters.deptCode === "130209") {
          this.$router.push({path: "/enrollment/addCaoFeiDian", query: {period: "junior"}});
        } else {
          this.$router.push({path: "/enrollment/addAd", query: {period: "junior"},});
        }
      }
    },
    // 导出报名信息
    /*exportEnrollInfo() {
      this.$download(
          `${pref}${this.prefixDeptCode}/biz/recruitStudent/exportInformationOfRegistration`,
          this.search,
          "xlsx"
          ,"导出报名信息.xlsx"
      ).then((res) => {
        this.$message.success("导出成功");
      })
    },*/
    exportEnrollInfo() {
      if (this.prefixDeptCode != "130423") {
        this.$download(
            `${pref}${this.prefixDeptCode}/biz/excelConfig/exportExcelPriMiddle`,
            this.search,
            "xlsx",
            "初中导出报名信息.xlsx"
        ).then((res) => {
          this.$message.success("导出成功");
        });
      } else {
        // 临漳130423
        this.$download(
            `${pref}${this.prefixDeptCode}/biz/recruitStudent/lzExportStudent`,
            this.search,
            "xlsx",
            "初中导出报名信息.xlsx"
        ).then((res) => {
          this.$message.success("导出成功");
        });
      }
    },
    // 多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 下载入户调查单
    exportHouseholdSurvey() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = {search: this.search, list: checkList};
			let url = `${pref}${this.prefixDeptCode}/biz/recruitStudent/batchExportHouseholdSurvey`
      this.$download(
				url,
				params,
				"zip",
				"下载入户调查单.zip"
      ).then((res) => {
        this.$message.success("下载成功");
      });
    },
    // 批量审核通过
    batchAudit() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = {
        search: Object.assign({}, this.search, {
          pageSize: undefined,
          pageNumber: undefined,
        }),
        list: checkList,
      };
      let msg =
          checkList.length == 0
              ? "确认完成审核？所有初审已通过的学生，教育局审核状态都会改为已通过，请谨慎使用。"
              : "确认为已选学生完成审核吗";
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        batchAudit(params, this.prefixDeptCode).then((res) => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 公示录取结果
    publicAdmission() {
      let checkList = this.multipleSelection.map((item) => item.studentBaseId);
      let params = {
        search: Object.assign({}, this.search, {
          pageSize: undefined,
          pageNumber: undefined,
        }),
        list: checkList,
      };
      publicity(params, this.prefixDeptCode).then((res) => {
        this.$confirm(res, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          publicAdmissionResults(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.getTableData();
          });
        });
      });
    },
    // 发送录取通知书
    notice() {
      this.switchModal("notice", true);
      this.$nextTick(() => {
        this.$refs.noticeForm.resetFields();
        this.noticeForm.content = "";
        this.noticeForm.num = "";
        this.search.publicityStatus=1;
        this.search.admissionLetterInStatus="0";
        let params = {
          search: Object.assign({}, this.search, {
            pageSize: undefined,
            pageNumber: undefined,

          }),
          type: 1,
          content: this.noticeForm.content,
        };
        sendRequisition(params, this.prefixDeptCode).then((res) => {
          this.noticeForm.num = res;
        });
        this.search.publicityStatus=``;
        this.search.admissionLetterInStatus=``;
      });
    },
    // 发送录取通知书确定
    confirmNotice() {
      this.$refs.noticeForm.validate((valid) => {
        if (valid) {
          this.search.publicityStatus=1;
          this.search.admissionLetterInStatus="0";
          let params = {
            search: Object.assign({}, this.search, {
              pageSize: undefined,
              pageNumber: undefined,

            }),
            type: 2,
            content: this.noticeForm.content,

          };
          sendRequisition(params, this.prefixDeptCode).then((res) => {
            this.$confirm("录取通知书成功发送 " + res + " 人", "提示", {
              confirmButtonText: "关闭",
              showCancelButton: false,
              type: "success",
              closeOnClickModal: false,
              showClose: false,
              closeOnPressEscape: false,
            }).then(() => {
              this.switchModal("notice", false);
              this.getTableData();
            });
          });
          this.search.publicityStatus=``;
          this.search.admissionLetterInStatus=``;
        }
      });
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      this.getAuditRecord();
      // this.getAuditStatus();
    },
    // 查询审核情况
    getAuditRecord() {
      this.auditRecordList = [];
      this.loadingAuditRecord = true;
      getAuditRecord(
          {key: this.curStuDetail.studentBaseId},
          this.prefixDeptCode
      )
          .then((res) => {
            this.auditRecordList = res;
          })
          .finally(() => {
            this.loadingAuditRecord = false;
          });
    },
    // 查询审核情况
    // getAuditStatus() {
    //   this.auditStatusList = [];
    //   this.loadingAuditStatus = true;
    //   getAuditStatus(
    //     { key: this.curStuDetail.studentBaseId },
    //     this.prefixDeptCode
    //   )
    //     .then((res) => {
    //       this.auditStatusList = res;
    //     })
    //     .finally(() => {
    //       this.loadingAuditStatus = false;
    //     });
    // },
    // 详情 - 下一条
    nextEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
          this.search.pageNumber * this.search.pageSize > this.total ||
          this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.$message.error("已是最后一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index < this.tableData.records.length - 1) {
          this.index += 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.search.pageNumber += 1;
          getEnrollList(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.curStuDetail = this.tableData.records[0];
            this.index = 0;
            this.getAuditRecord();
          });
        }
      }
    },
    // 详情 - 上一条  2024.12.16
    prevEnrollDetail() {
      // 当前是不可翻页的页码（最后一页）
      if (
          this.search.pageNumber * this.search.pageSize > this.total ||
          this.search.pageNumber * this.search.pageSize == this.total
      ) {
        if (this.index > 0) {
          this.index -= 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.$message.error("已是第一条数据");
        }
      }
      // 当前是可翻页的页码
      else {
        if (this.index > 0) {
          this.index -= 1;
          this.curStuDetail = this.tableData.records[this.index];
          this.getAuditRecord();
        } else {
          this.search.pageNumber -= 1;
          getEnrollList(this.search, this.prefixDeptCode).then((res) => {
            this.tableData = res;
            this.index = this.tableData.records.length - 1;
            this.curStuDetail = this.tableData.records[this.index];
            this.getAuditRecord();
          });
        }
      }
    },
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
    // 撤销公示结果
    revokePublic(row) {
      this.switchModal("revokePublic", true);
      this.$nextTick(() => {
        this.$refs.revokePublicForm.resetFields();
        this.revokePublicForm.id = row.studentBaseId;
      });
    },
    // 撤销公示结果确认
    confirmRevokePublic() {
      this.$refs.revokePublicForm.validate((valid) => {
        if (valid) {
          let params = this.revokePublicForm;
          revokePublic(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("revokePublic", false);
            this.getTableData();
          });
        }
      });
    },
    // 删除报名信息
    deleteSign(row) {
      this.switchModal("deleteSign", true);
      this.$nextTick(() => {
        this.$refs.deleteSignForm.resetFields();
        this.deleteSignForm.key = row.studentBaseId;
      });
    },
    // 删除报名信息确认
    confirmDeleteSign() {
      this.$refs.deleteSignForm.validate((valid) => {
        if (valid) {
          let params = this.deleteSignForm;
          deleteEnroll(params, this.prefixDeptCode).then((res) => {
            this.$message.success("操作成功");
            this.switchModal("deleteSign", false);
            this.getTableData();
          });
        }
      });
    },
    // 查询功能开关状态 初中
    getFuncStatusInfo(params) {
      getFuncStatus({key: params}, this.prefixDeptCode).then((res) => {
        if (params == "DELETE_STUDENT_JUNIOR") {
          this.funcStatusInfo.deleteStatus = res;
        } else if (params == "PUBLIC_STUDENT_JUNIOR") {
          this.funcStatusInfo.publicStatus = res;
        } else if (params == "NOTICE_STUDENT_JUNIOR") {
          this.funcStatusInfo.noticeStatus = res;
        }
      });
    },
    // 获取审核情况列表中指定的报名详情
    getEnrollRecords(row) {
      this.curStuRecordId = row.id
      this.switchModal("enrollRecord", true)
    },
    isDisabledForTownshipSchool(row) {
      // 检查是否为滦州区(130284)的乡镇学校
      const isTownshipSchool = this.isTownshipSchool(row);
      
      // 只有教育局账户(COUNTY_ADMIN或AUDITOR)且是滦州区的乡镇学校时才禁用
      const isDisabled = this.prefixDeptCode === '130284' && 
                        (this.role === 'COUNTY_ADMIN' || this.role === 'AUDITOR') && 
                        !this.search.enrollSchoolId && 
                        isTownshipSchool;
      
      return isDisabled;
    },
    
    // 判断是否为乡镇学校
    isTownshipSchool(row) {
      return row.nature === '乡镇' || 
        row.nature === '乡镇学校' || 
        row.nature === 1 || 
        row.nature === '1' ||
        (typeof row.nature === 'string' && row.nature.includes('乡镇'));
    },
    
    // 判断是否为滦州乡镇学校
    isLuanZhouTownshipSchool() {
      const userInfo = this.$store.getters.userInfo;
      const natureValue = userInfo?.deptInfo?.nature;
      return this.prefixDeptCode === '130284' && 
             this.role === 'SCHOOL' && 
             (natureValue === '1' || natureValue === 1);
    },
    
    // 判断是否为滦州城区学校
    isLuanZhouCitySchool() {
      const userInfo = this.$store.getters.userInfo;
      const natureValue = userInfo?.deptInfo?.nature;
      return this.prefixDeptCode === '130284' && 
             this.role === 'SCHOOL' && 
             natureValue !== '1' && natureValue !== 1;
    },
    
    // 控制通过按钮禁用状态（教育局）
    canDisablePass(row) {
      // 只有滦州乡镇学校可以反复操作
      if (this.prefixDeptCode === '130284' && this.isTownshipSchool(row)) {
        // 如果当前已是通过状态，则禁用通过按钮
        if (row.educationReviewStatus === '通过') {
          console.log('乡镇学校通过状态，禁用按钮');
          return true;
        }
        return false;
      }
      // 滦州城区学校和其他学校按原逻辑处理 - 已通过状态禁用通过按钮
      return row.educationReviewStatus == '通过';
    },
    
    // 控制不通过按钮禁用状态（教育局）
    canDisableReject(row) {
      // 只有滦州乡镇学校可以反复操作
      if (this.prefixDeptCode === '130284' && this.isTownshipSchool(row)) {
        return false;
      }
      // 滦州城区学校和其他学校按原逻辑处理 - 已不通过状态禁用不通过按钮
      return row.educationReviewStatus == '不通过';
    },
    
    // 控制通过按钮禁用状态（学校）
    canDisableSchoolPass(row) {
      // 安全检查，确保row存在且不是Observer对象
      if (!row || !row.schoolReviewStatus) {
        console.log('行数据无效或缺少schoolReviewStatus字段');
        return true; // 数据不完整时禁用按钮
      }
      
      console.log('学校审核状态:', row.schoolReviewStatus);
      
      // 滦州区乡镇学校特殊处理
      const userInfo = this.$store.getters.userInfo;
      const natureValue = userInfo?.deptInfo?.nature;
      const isLuanzhouTownship = this.prefixDeptCode === '130284' && 
                                 this.role === 'SCHOOL' && 
                                 (natureValue === '1' || natureValue === 1);
      
      if (isLuanzhouTownship) {
        // 乡镇学校，如果当前已是通过状态，则禁用通过按钮
        if (row.schoolReviewStatus === '通过') {
          console.log('乡镇学校通过状态，禁用按钮');
          return true;
        }
        console.log('乡镇学校非通过状态，不禁用按钮');
        return false;
      } else {
        // 非滦州乡镇学校，只有待审核状态可以点击通过按钮
        const shouldDisable = row.schoolReviewStatus !== '待审核';
        console.log('非滦州乡镇学校状态:', row.schoolReviewStatus, '是否禁用:', shouldDisable);
        return shouldDisable;
      }
    },
    
    // 控制不通过按钮禁用状态（学校）
    canDisableSchoolReject(row) {
      // 安全检查，确保row存在且不是Observer对象
      if (!row || !row.schoolReviewStatus) {
        console.log('行数据无效或缺少schoolReviewStatus字段');
        return true; // 数据不完整时禁用按钮
      }
      
      // 滦州区乡镇学校特殊处理
      const userInfo = this.$store.getters.userInfo;
      const natureValue = userInfo?.deptInfo?.nature;
      const isLuanzhouTownship = this.prefixDeptCode === '130284' && 
                                 this.role === 'SCHOOL' && 
                                 (natureValue === '1' || natureValue === 1);
      
      if (isLuanzhouTownship) {
        // 乡镇学校可以反复操作
        return false;
      } else {
        // 非滦州乡镇学校，只有待审核状态可以点击不通过按钮
        const shouldDisable = row.schoolReviewStatus !== '待审核';
        console.log('非滦州乡镇学校状态:', row.schoolReviewStatus, '是否禁用:', shouldDisable);
        return shouldDisable;
      }
    },
    
    // 判断是否显示公示录取结果按钮
    showPublicAdmissionButton() {
      // 调试日志
      console.log('公示按钮判断 - 区县代码:', this.prefixDeptCode);
      console.log('公示按钮判断 - 角色:', this.role);
      console.log('公示按钮判断 - 功能开关状态:', this.funcStatusInfo.publicStatus);
      
      // 区县教育局账户可以看到公示按钮（保持原有逻辑）
      if (this.role === 'COUNTY_ADMIN' && this.funcStatusInfo.publicStatus) {
        console.log('显示公示按钮: 区县教育局账户');
        return true;
      }
      
      // 滦州乡镇学校可以看到公示按钮
      const userInfo = this.$store.getters.userInfo;
      const natureValue = userInfo?.deptInfo?.nature;
      
      if (this.prefixDeptCode === '130284' && 
          this.role === 'SCHOOL' && 
          (natureValue === '1' || natureValue === 1)) {
        console.log('显示公示按钮: 滦州乡镇学校');
        return true;
      }
      
      // 其他账户不显示
      console.log('不显示公示按钮: 非区县教育局且非滦州乡镇学校');
      return false;
    },
    // 格式化意向地显示
    formatIntentionCity(value) {
      if (!value) return '-';
      // 如果是字符串形式的数字，说明是逗号分隔的地址编码
      if (typeof value === 'string' && value.includes(',')) {
        const codes = value.split(',');
        const result = [];
        // 遍历省市区数据查找对应的名称
        for (let i = 0; i < codes.length; i++) {
          const code = codes[i];
          let found = false;
          // 遍历省级
          for (const province of this.areaOptions) {
            if (province.value.toString() === code) {
              result.push(province.label);
              found = true;
              break;
            }
            // 遍历市级
            if (province.children) {
              for (const city of province.children) {
                if (city.value.toString() === code) {
                  result.push(city.label);
                  found = true;
                  break;
                }
                // 遍历区级
                if (city.children) {
                  for (const district of city.children) {
                    if (district.value.toString() === code) {
                      result.push(district.label);
                      found = true;
                      break;
                    }
                  }
                }
                if (found) break;
              }
            }
            if (found) break;
          }
        }
        return result.join(' / ') || value;
      }
      // 如果是数组，直接返回数组值的拼接
      if (Array.isArray(value)) {
        return value.join(' / ');
      }
      // 其他情况直接返回原值
      return value || '-';
    },
    // 根据学校ID获取学校名称
    getSchoolName(schoolId) {
      if (!schoolId) return '-';
      const school = this.primarySchoolList.find(item => item.id === schoolId || item.deptName === schoolId);
      return school ? school.deptName : schoolId;
    }
  },
};
</script>

<style lang="scss" scoped>
.tips {
  padding-left: 30px;
  padding-bottom: 30px;
  font-size: 12px;
}

.notice-num {
  font-size: 16px;
}

.signNum {
  color: #606266;
  font-size: 13px;
  height: 28px;
  line-height: 28px;
}
</style>
