<template>
  <div class="section">
    <div>
      <el-tabs type="border-card" v-model="type">
        <el-tab-pane label="唐山各区户籍">
          <p class="live-label">拥有唐山各区户籍的儿童/少年只需上传居住地居委会开具的居住证明</p>
          <el-form ref="liveForm" :model="liveForm">
            <el-form-item label="" prop="liveCert">
              <imgUpload :keyName="`liveCert`" :keyCn="`居住证明`" :isRequire="true" :multiple="false" :limit="1"
                         @value-change="valueChange"></imgUpload>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="唐山各县及外省市户籍">
          <el-form ref="form" :model="form" size="small" :inline="true" :rules="rules">
            <el-row>
              <el-col style="margin-top: 20px">
                <el-form-item label="居住证编号" prop="code">
                  <el-input v-model="form.code"></el-input>
                </el-form-item>
              </el-col>
              <el-col style="margin-top: 20px">
                <el-form-item label="居住证持有者" prop="holder">
                  <el-input v-model="form.holder"></el-input>
                </el-form-item>
              </el-col>
              <el-col style="margin-top: 20px">
                <el-form-item label="与学生关系" prop="relation">
                  <el-select v-model="form.relation">
                    <el-option v-for="item in relationOption" :key="item.id" :label="item.val"
                               :value="item.val"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col style="margin-top: 20px">
                <el-form-item label="居住证地址" prop="address">
                  <el-input v-model="form.address"></el-input>
                </el-form-item>
              </el-col>
              <el-col style="margin-top: 20px">
                <el-form-item label="持有者身份证号" prop="idCard">
                  <el-input v-model="form.idCard"></el-input>
                </el-form-item>
              </el-col>
              <el-col style="margin-top: 20px">
                <el-form-item label="" prop="liveCert">
                  <imgUpload :keyName="`liveCert`" :keyCn="`居住证`" :isRequire="true" :multiple="false" :limit="1"
                             @value-change="valueChange"></imgUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <div class="b-justify-center">
          <el-button type="primary" @click="next">下 一 步</el-button>
        </div>
      </el-tabs>

    </div>
  </div>
</template>

<script>
import imgUpload from "@/components/Form/imgUpload.vue";
import {liveRelationList} from "@/utils/dictionary";

export default {
  components: {
    imgUpload
  },
  data() {
    return {
      type: 0,
      form: {},
      liveForm: {},
      rules: {
        code: [
          {required: true, message: '请输入居住证编号', trigger: 'blur'}
        ],
        holder: [
          {required: true, message: '请输入居住证持有者', trigger: 'blur'}
        ],
        relation: [
          {required: true, message: '请输入与学生关系', trigger: 'blur'}
        ],
        address: [
          {required: true, message: '请输入居住证地址', trigger: 'blur'}
        ],
        idCard: [
          {required: true, message: '请输入持有者身份证号', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (!/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
                callback(new Error('请输入正确的身份证号'));
              } else {
                callback();
              }
            }
            , trigger: 'blur'
          }
        ]
      },
      relationOption: liveRelationList,
    }
  },
  methods: {
    async next() {
      const liveFlag = this.type === "0";
      const formRef = liveFlag ? this.$refs.liveForm : this.$refs.form;
      const formData = liveFlag ? this.liveForm : this.form;

      try {
        if (!formData.liveCert) {
          this.$message.error(liveFlag ? '请上传居住证明' : '请上传居住证');
          return;
        }
        const valid = await formRef.validate();
        if (!valid) return;

        const liveInfo = {
          liveType: this.type,
          liveOption: formData
        };
        this.$emit('nextAdForm', liveInfo);
        // this.$store.commit('setLiveInfo', liveInfo);
        // await this.$router.push("/enrollSchool");
      } catch (error) {
        console.error('errMsg:', error);
      }
    },
    valueChange(param) {
      let {id, val} = param;
      if (this.type === '0') {
        this.liveForm[id] = val;
      } else {
        this.form[id] = val;
      }
    },
  }
}
</script>

<style>
.live-label {
  text-align: center;
  margin-bottom: 10px;
}
</style>