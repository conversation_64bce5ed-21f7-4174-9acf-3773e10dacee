<template>
  <div>
    <CommonHome v-if="role == 'COUNTY_ADMIN' && !isFive" />
    <div v-else-if="role == 'SCHOOL'">{{schoolName}}</div>
    <div v-else>唐山区县招生</div>
  </div>
</template>

<script>
import CommonHome from "./components/CommonHome";
export default {
  name: "home",
  components: { CommonHome},
  data() {
    return {
      role: this.$store.getters.role,
      isFive: this.$store.getters.isFive,
      schoolName: ""
    };
  },
  created() {
    if(this.role == 'SCHOOL') {
      this.schoolName = this.$store.getters.userInfo.deptName
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
