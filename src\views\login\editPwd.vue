<template>
  <div class="container-box">
    <div class="title">
      <div
        style="
          color: white;
          font-size: 50px;
          align: center;
          text-shadow: 3px 3px 3px rgba(148, 150, 150, 0.99);
        "
      >
        唐山市教育入学一件事服务平台管理后台
      </div>
    </div>
    <div class="logo-box" style="position: relative; top: 32%">
      <h3>修 改 密 码</h3>
      <div class="line"></div>
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-position="right"
        label-width="80px"
        inline
        @keyup.enter.native="submitForm"
      >
        <el-form-item label="账号">
          <span>{{ username }}</span>
        </el-form-item>
        <el-form-item prop="oldPassword" label="原密码">
          <el-input
            v-model.trim="form.oldPassword"
            type="password"
            placeholder="原密码"
          ></el-input>
        </el-form-item>
        <el-form-item prop="newPassword" label="新密码">
          <el-input
            v-model.trim="form.newPassword"
            type="password"
            placeholder="新密码"
          ></el-input>
        </el-form-item>
        <el-form-item prop="confirmPassword" label="确认密码">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="确认密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="submitForm" style="width: 100"
        >确&ensp;&ensp;&ensp;&ensp;定</el-button
      >
      <el-row
        type="flex"
        justify="space-between"
        align="middle"
        style="margin: 10px 0"
      >
      </el-row>
    </div>
  </div>
</template>

<script>
import { changePassword } from "@/api/login";
export default {
  data() {
    var validPass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          callback();
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    var validSurePass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("确认密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          if (this.form.newPassword) {
            if (value == this.form.newPassword) {
              callback();
            } else {
              return callback(new Error("两次输入的密码不一致"));
            }
          }
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    return {
      form: {
        // userId: "",
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
      rules: {
        oldPassword: [
          { required: true, trigger: "blur", message: "请输入密码" },
        ],
        newPassword: [
          { required: true, trigger: "blur", validator: validPass },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", validator: validSurePass },
        ],
      },
    };
  },
  created() {
    var that = this;
    document.onkeydown = function (e) {
      var key = window.event.keyCode;
      if (key === 13) {
        that.submitForm(); // 触发事件
      }
    };
    // setTimeout(() => {
    //   this.form.userId = this.$store.getters.userInfo.id;
    //   console.log(this.form.userId);
    // }, 1500);
  },
  computed: {
    username() {
      return this.$store.getters.userInfo.username;
    },
  },
  methods: {
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          changePassword(this.form).then((res) => {
            this.$alert("密码修改成功，请重新登录", "提示", {
              confirmButtonText: "确定",
              callback: () => {
                this.$store.dispatch("user/logout").then(() => {
                  this.$router.push("/login");
                });
              },
            });
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.container-box {
  width: 100%;
  height: 100vh;
  position: relative;
  padding-top: 50px;
  box-sizing: border-box;
  background: url("../../assets/images/login.jpg") no-repeat center / 100% 100%;
}

.container-box .title {
  margin-top: 42px;
  text-align: center;
}

.logo-box {
  width: 400px;
  padding: 20px 40px;
  background-color: #fff;
  border-radius: 5px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
}
.logo-box h3 {
  color: #305893;
  text-align: center;
  margin-top: 0;
}

.logo-box .line {
  border-bottom: 1px solid #e2e3e7;
  margin-bottom: 22px;
}
.el-button {
  width: 100%;
}
.el-form-item {
  padding-bottom: 30px;
}
</style>
