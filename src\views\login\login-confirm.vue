<template>
  <div class="sd-wechat-container" v-loading="showLoading">
    <div class="sd-qrcode-container">
      <div class="sd-title">登录确认</div>
      <div id="sd-wechat-qrcode"></div>
      <div class="sd-warning-text sd-text-center">
        使用管理员微信扫码进行身份认证
      </div>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-back"
        plain
        @click="back"
        style="margin-top: 10px"
        >返回</el-button
      >
    </div>
  </div>
</template>

<script>
import { showLoginForm } from "@/views/login/wxLoginForm";

export default {
  data() {
    return {
      showLoading: true,
    };
  },
  created() {
    setTimeout(() => (this.showLoading = false), 1000);
    const token = this.$store.getters.token;
    if (!token) {
      this.$router.push("/login");
    } else {
      this.showWechatForm();
    }
  },
  methods: {
    back() {
      this.$store.dispatch("user/logout").then(() => {
        this.$router.push("/login");
      });
    },
    showWechatForm() {
      const cssText =
        "LmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5Om5vbmU7fQouaW1wb3dlckJveCAucXJjb2RlIHtoZWlnaHQ6MjAwcHg7d2lkdGg6MjAwcHg7dXNlci1zZWxlY3Q6bm9uZTt9Ci5pbXBvd2VyQm94IC5pbmZve3dpZHRoOiBhdXRvO30KLmltcG93ZXJCb3ggLmluZm8gLnN0YXR1c19icm93c2VyIHA6bnRoLWNoaWxkKDIpe2Rpc3BsYXk6IG5vbmU7fQoudHBsX2Zvcl9pZnJhbWUge3VzZXItc2VsZWN0Om5vbmU7fQ==";
      showLoginForm("sd-wechat-qrcode", "/admin/#/login-confirm-auth", cssText);
    },
  },
};
</script>

<style lang="scss" scoped>
.sd-wechat-container {
  height: 100vh;
  width: 100%;
  background-color: #8c939d;
  display: flex;
  justify-content: center;
  align-items: center;

  .sd-qrcode-container {
    width: 500px;
    height: 600px;
    background-color: #eeeeee;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    border-radius: 10px;

    .sd-title {
      height: 120px;
      line-height: 120px;
      font-size: 24px;
      font-weight: bold;
    }

    .sd-warning-text {
      position: absolute;
      top: 450px;
    }
  }
}
</style>
