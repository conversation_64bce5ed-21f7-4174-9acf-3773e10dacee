<template>
  <div class="container-box">
    <div class="title">唐山市教育入学一件事服务平台管理后台</div>
    <div class="login-box">
      <div class="login-title">后 台 登 录</div>
      <div class="line"></div>
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-position="left"
        @keyup.enter.native="submitForm"
      >
        <el-form-item prop="username">
          <el-input
            v-model.trim="form.username"
            prefix-icon="el-icon-user"
            style="width: 100"
            placeholder="账号"
          ></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            prefix-icon="el-icon-lock"
            style="width: 100"
            type="password"
            placeholder="密码"
          ></el-input>
        </el-form-item>
        <el-form-item prop="verifyCode">
          <el-row type="flex" align="middle">
            <el-col
              ><el-input
                v-model="form.verifyCode"
                style="width: 170px; margin-right: 10px"
              ></el-input
            ></el-col>
            <el-col>
              <!-- <div style="height: 40px" @click="refreshCode()">
                <Identify :identifyCode="identifyCode" />
              </div> -->
              <!-- <img :src="verifyCodeImageUrl" alt=""> -->
              <img
                :src="verifyCodeImageUrl"
                alt=""
                style="position: relative; top: 6px"
                @click="refreshCode"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <el-button
        type="primary"
        @click="submitForm"
        style="width: 100%"
        :loading="loading"
        >登&ensp;&ensp;&ensp;&ensp;录</el-button
      >
      <el-row type="flex" justify="end" align="middle">
        <!-- <span class="forget" @click="forgetPwd">忘记密码</span> -->
      </el-row>
    </div>
  </div>
</template>

<script>
// import Identify from "@/components/Identify";
// import { identifyCodes } from "@/utils/common";
import { getVerifyCodeImage } from "@/api/login";
export default {
  data() {
    // var validCode = (rule, value, callback) => {
    //   if (!value) {
    //     return callback(new Error("图形验证码不能为空"));
    //   } else {
    //     if (this.identifyCode == value) {
    //       callback();
    //     } else {
    //       this.refreshCode();
    //       return callback(new Error("图形验证码有误"));
    //     }
    //   }
    // };
    return {
      loading: false,
      showLoading: false,
      form: {
        username: "",
        password: "",
        verifyCode: "",
        code: "",
      },
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
        ],
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        verifyCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" },
        ],
      },
      // identifyCode: "",
      // identifyCodes: identifyCodes,
      // baseApi: process.env.VUE_APP_BASE_API,
      verifyCodeImageUrl: "",
    };
  },
  // components: { Identify },
  created() {

  },
  mounted() {
    this.refreshCode();
  },
  methods: {
    // 账号登录
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("user/login", this.form)
            .then(res => {
							console.log(res)
							// 海港区跳转短信验证

								this.$router.push("/")

              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
              this.refreshCode();
            });
        } else {
          this.refreshCode();
          return false;
        }
      });
    },
    //刷新验证码
    refreshCode() {
      // let getTimestamp = new Date().getTime();
      getVerifyCodeImage().then((res) => {
        this.verifyCodeImageUrl = res.imgData;
        this.form.code = res.code;
      });
    },
    // 更新验证码
    // refreshCode() {
    //   // this.identifyCode = "";
    //   // this.makeCode(this.identifyCodes, 4);
    // },
    // 随机生成验证码字符串
    // makeCode(data, len) {
    //   for (let i = 0; i < len; i++) {
    //     this.identifyCode +=
    //       this.identifyCodes[this.randomNum(0, this.identifyCodes.length - 1)];
    //   }
    // },
    // 生成随机数
    // randomNum(min, max) {
    //   max = max + 1;
    //   return Math.floor(Math.random() * (max - min) + min);
    // },
    // 忘记密码
    forgetPwd() {
      this.$router.push("/forgetPwd");
    },
  },
};
</script>

<style lang="scss" scoped>
.container-box {
  width: 100%;
  height: 100vh;
  position: relative;
  padding-top: 50px;
  box-sizing: border-box;
  background: url("../../assets/images/login.jpg") no-repeat center / 100% 100%;
  .title {
    margin-top: 42px;
    text-align: center;
    color: white;
    font-size: 50px;
    text-shadow: 3px 3px 3px rgba(148, 150, 150, 0.99);
  }
}
.login-box {
  width: 400px;
  padding: 20px 40px;
  background-color: #fff;
  border-radius: 5px;
  position: absolute;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
  position: relative;
  top: 32%;
  left: 50%;
  .login-title {
    color: #305893;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    padding-bottom: 15px;
  }
  .line {
    border-bottom: 1px solid #e2e3e7;
    margin-bottom: 22px;
  }
  .forget {
    font-size: 14px;
    color: #909399;
    padding-top: 4px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
  }
}
/* .remeber {
  font-size: 12px;
  margin-left: 5px;
} */
/* .footer {
  width: 100%;
  height: 96px;
  background: rgba(28, 88, 139, 0.92);
  position: absolute;
  bottom: 0;
}
.footer-content {
  max-width: 1400px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.footer-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.footer-center p {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #ffffff;
  display: inline-block;
  margin: 0 15px;
  opacity: 0.5;
}
.beian {
  color: #fff;
  text-decoration: underline;
} */
</style>
