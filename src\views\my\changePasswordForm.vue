<!-- 组件用途 -->
<template>
  <div v-loading="saveLoading">
    <el-row>
      <el-col :span="16" :offset="3">
        <el-form ref="form" :rules="rules" :model="form" label-width="100px">
          <el-form-item label="旧密码" prop="oldPassword">
            <el-input
              v-model="form.oldPassword"
              placeholder="请输入旧密码"
              type="password"
              maxlength="64"
              clearable
              show-word-limit
              show-password
              auto-complete="new-password"
            ></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="form.newPassword"
              placeholder="请输入新密码"
              type="password"
              maxlength="64"
              clearable
              show-word-limit
              show-password
              auto-complete="new-password"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              placeholder="请输入确认密码"
              type="password"
              maxlength="64"
              clearable
              show-word-limit
              show-password
              auto-complete="new-password"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import FormMixin from "@/mixins/FormMixin";
import { changePassword } from "@/api/login";
export default {
  name: "changePasswordForm",
  mixins: [FormMixin],
  data() {
    var validPass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          callback();
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    var validSurePass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("确认密码不能为空"));
      } else {
        let reg =
          /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{6,12}$/;
        if (reg.test(value)) {
          if (this.form.newPassword) {
            if (value == this.form.newPassword) {
              callback();
            } else {
              return callback(new Error("两次输入的密码不一致"));
            }
          }
        } else {
          return callback(
            new Error("密码必须由 “字母+数字+特殊符号!@#$%^&*” 组成，6~12位")
          );
        }
      }
    };
    return {
      form: {
        // userId: "",
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
      rules: {
        oldPassword: [
          { required: true, trigger: "blur", message: "请输入密码" },
        ],
        newPassword: [
          { required: true, trigger: "blur", validator: validPass },
        ],
        confirmPassword: [
          { required: true, trigger: "blur", validator: validSurePass },
        ],
      },
    };
  },
  created() {
    this.$nextTick(() => {
      this.$refs.form.resetFields();
    });
  },
  methods: {
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          changePassword(this.form)
            .then(() => this.$emit("save-complete", 1))
            .catch((err) => {
              this.$emit("save-complete", 0);
            });
        }
      });
    },
  },
};
</script>
<style lang='scss' scoped>
.el-form-item {
  padding-bottom: 10px;
}
</style>