<!-- 组件用途 -->
<template>
  <div class="sd-personal" v-loading="detailLoading">
    <div font-size-6>个人信息</div>
    <el-form w-200 m-t-4 m-l-4 label-width="auto" :model="form" ref="form">
      <el-form-item label="账号："> {{ form.username }} </el-form-item>
      <el-form-item label="角色：">
        <el-tag v-for="(role, index) in userInfo.roles" :key="index">{{
          role.roleName
        }}</el-tag>
      </el-form-item>
      <el-form-item label="真实姓名：" prop="nickname">
        <div>
          <span>{{ form.nickname }}</span>
        </div>
      </el-form-item>
      <el-form-item label="手机号：" prop="mobile">
        <div>
          <span>{{ form.mobile }}</span>
        </div>
      </el-form-item>
      <el-form-item label="账户状态：">
        <el-tag :type="userInfo.status == 1 ? 'success' : 'danger'">
          {{ userInfo.status == 1 ? "正常" : "禁用" }}
        </el-tag>
      </el-form-item>
      <el-form-item label="密码状态：">
        <el-tag :type="userInfo.defaultPasswordFlag ? 'warning' : 'success'">
          {{ userInfo.defaultPasswordFlag ? "默认密码" : "非默认" }}
        </el-tag>
      </el-form-item>
      <el-form-item class="no-border">
        <el-button
          m-l-4
          type="warning"
          icon="el-icon-edit"
          size="small"
          @click="dialogFormVisible = true"
          >修改密码</el-button
        >
      </el-form-item>
    </el-form>

    <el-dialog
      title="修改密码"
      center
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <div>
        <component
          :ref="'changePasswordForm'"
          :is="'changePasswordForm'"
          :data="userInfo"
          :mode="'EDIT'"
          v-if="dialogFormVisible"
          @save-complete="changeComplete"
        />
      </div>
      <div class="flex-center">
        <el-button
          type="default"
          @click="dialogFormVisible = false"
          size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="() => this.$refs['changePasswordForm'].save()"
          :loading="changePasswordLoading"
          size="small"
          >保 存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { emailValidator, mobileValidator } from '@/utils/validator'
import { mapGetters } from "vuex";
import ChangePasswordForm from "./changePasswordForm.vue";

export default {
  name: "personal",
  components: { ChangePasswordForm },
  data() {
    return {
      form: {
        username: null,
        nickname: null,
        mobile: null,
        status: null,
      },
      // rules: {
      //     nickname: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
      //     mobile: [{ required: true, message: '请输入手机号码', trigger: 'blur' }, { validator: mobileValidator }],
      //     email: [{ required: true, message: '请输入电子邮箱', trigger: 'blur' }, { validator: emailValidator }]
      // },
      detailLoading: false,
      dialogFormVisible: false,
      changePasswordLoading: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      /* this.detailLoading = true
            const data = await this.$apis.user.detail(this.userInfo.id)
            const { id, username, nickname, email, status, roles, avatar, mobile } = data
            this.form = { id, username, nickname, email, status, roleIds: roles.map((item) => item.id), avatar, mobile }
            this.detailLoading = false */
      this.form.username = this.userInfo.username;
      this.form.nickname = this.userInfo.nickname;
      this.form.mobile = this.userInfo.mobile;
      this.form.status = this.userInfo.status;
    },
    saveSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.detailLoading = true;
          this.$apis.user
            .update(this.form)
            .then(() => {
              this.$message.success("修改成功");
            })
            .catch(() => this.$message.error("修改失败"))
            .finally(() => (this.detailLoading = false));
        }
      });
    },
    changeComplete(status) {
      this.dialogFormVisible = false;
      if (status == 1) {
        this.$alert("修改密码成功", "温馨提示", {
          confirmButtonText: "确定",
          callback: async () => {
            await this.$store.dispatch("user/logout");
            await this.$store.dispatch("settings/clearState");
            await this.$router.push({ path: "/login" });
          },
        });
      }
    },
  },
};
</script>

<style lang='scss' scoped>
.sd-personal {
  .el-form-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  }

  .el-form-item.no-border {
    border-bottom: 0;
    text-align: center;
  }
}
</style>