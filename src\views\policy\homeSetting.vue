<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left"></div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="title">
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="搜索标题"
              class="sd-w-200"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="标题"
        prop="title"
      ></el-table-column>
      <el-table-column
        align="center"
        label="创建时间"
        prop="createTime"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200px">
        <template slot-scope="{ row }">
          <el-link
            size="small"
            type="primary"
            :underline="false"
            icon="el-icon-view"
            style="margin-right: 10px"
            @click="detail(row)"
            >详情</el-link
          >
          <el-link
            size="small"
            type="warning"
            :underline="false"
            icon="el-icon-edit"
            style="margin-right: 10px"
            @click="edit(row)"
            v-if="role == 'COUNTY_ADMIN'"
            >编辑</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="sd-pagination-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 详情 -->
    <el-dialog
      title="首页设置"
      :visible.sync="modal.detail"
      center
      :close-on-click-modal="false"
      width="1000px"
    >
      <el-form>
        <el-form-item prop="title" label="标题：">
          {{ poDetail.title }}
        </el-form-item>
        <el-form-item label="内容：" class="editor-label">
          <div v-html="poDetail.content"></div>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 新增，修改 -->
    <el-dialog
      title="首页设置"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="1000px"
    >
      <el-form :model="addForm" ref="addForm" :rules="rules">
        <el-form-item prop="title" label="标题：">
          <el-input
            v-model.trim="addForm.title"
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="内容：" class="editor-label">
          <div ref="editor"></div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { policyList, updatePolicy, policyDetail } from "@/api/policy";
import Editor from "wangeditor";
import { pref } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      role: this.$store.getters.role,
      search: {
        type: 2,
        keywords: "",
      },
      addForm: {
        id: "",
        title: "",
        content: "",
        type: 2,
      },
      poDetail: {},
      editor: null,
      modal: {
        addOrEdit: false,
        detail: false,
      },
      rules: {
        title: [
          {
            required: true,
            message: "请输入公告标题",
            trigger: "blur",
          },
        ],
        content: [
          {
            required: true,
            message: "请输入公告内容",
            trigger: "blur",
          },
        ],
      },
    };
  },
  destroyed() {
    if (this.editor) {
      this.editor.destroy();
    }
  },
  methods: {
    // 列表
    getTableData() {
      policyList(this.search, this.prefixDeptCode).then((res) => {
        this.tableData = res;
      });
    },
    // 编辑
    edit(row) {
      this.addForm.id = row.id;
      this.addForm.title = row.title;
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        if (this.editor == null) {
          this.editorCreate();
          this.editor.txt.html(row.content);
        } else {
          this.editor.destroy();
          this.editorCreate();
          this.editor.txt.html(row.content);
        }
      });
    },
    // 详情
    detail(row) {
      this.switchModal("detail", true);
      policyDetail({key: row.id}, this.prefixDeptCode).then(res => {
        this.poDetail= res;
      })
    },
    // 更新
    confirmUpdate() {
      this.$refs["addForm"].validate((valid) => {
        if (valid) {
          updatePolicy(this.addForm, this.prefixDeptCode).then(() => {
            this.switchModal("addOrEdit", false);
            this.$message.success("操作成功");
            this.getTableData();
          });
        } else {
          return false;
        }
      });
    },
    // 创建富文本
    editorCreate() {
      var _this = this;
      this.editor = new Editor(this.$refs.editor);
      this.editor.config.onchange = (html) => this.editorCatchData(html);
      this.editor.config.height = 400;
      this.editor.config.showLinkImg = false;
      this.editor.config.debug = true;
      this.editor.config.uploadImgServer = `${this.baseApi}${pref}${this.prefixDeptCode}/biz/file/uploadImg`;
      this.editor.config.uploadFileName = "file";
      this.editor.config.uploadImgMaxSize = 5 * 1024 * 1024;
      this.editor.config.uploadImgHeaders = {
        'Authorization': `${this.$store.getters.token}`,
        'client-id': 'admin',
        'state': this.$store.getters.securityKey
      };
      this.editor.config.uploadImgHooks = {
        fail: function (xhr, editor, result) {
        },
        success: function (xhr, editor, result) {
        },
        customInsert: function (insertImgFn, result) {
          insertImgFn(
            _this.baseApi + pref + _this.prefixDeptCode + result.data
          );
        },
      };
      this.editor.config.menus = [
        // 菜单配置
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        "code", // 插入代码
        "undo", // 撤销
        "redo", // 重复
      ];
      // 创建富文本实例
      this.editor.create();
    },
    // 更新content
    editorCatchData(content) {
      this.addForm.content = content;
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-form--inline .el-form-item {
    margin-bottom: 0 !important;
  }
}
.po-detail-txt {
  word-break: break-all;
}
.editor-label {
  ::v-deep .el-form-item__label {
    float: none;
  }
}
</style>