<!-- 操作日志 -->
<template>
  <div>
    <div class="sd-option-container">
<!--      <div class="sd-options">-->
<!--        <el-button-->
<!--            size="small"-->
<!--            type="warning"-->
<!--            icon="el-icon-download"-->
<!--            @click="exportLog"-->
<!--            :loading="downloadLoading"-->
<!--        >导出</el-button-->
<!--        >-->
<!--      </div>-->
      <div class="sd-search">
          <el-input
              size="small"
              v-model.trim="search.signId"
              placeholder="报名ID"
              class="sd-w-200"
              clearable
          ></el-input>
          <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="姓名或身份证号"
              class="sd-w-200"
              clearable
          ></el-input>
          <el-input
              size="small"
              v-model.trim="search.creatorName"
              placeholder="操作账号"
              class="sd-w-200"
              clearable
          ></el-input>
<!--        <el-date-picker-->
<!--            v-model="search.startDateTime"-->
<!--            type="datetime"-->
<!--            placeholder="选择开始时间"-->
<!--            :picker-options="pickerBeginOptions(search.endDateTime)"-->
<!--            value-format="yyyy-MM-dd HH:mm:ss"-->
<!--            style="width: 200px"-->
<!--            size="small"-->
<!--        >-->
<!--        </el-date-picker>-->
<!--        <el-date-picker-->
<!--            v-model="search.endDateTime"-->
<!--            type="datetime"-->
<!--            placeholder="选择结束时间"-->
<!--            :picker-options="pickerEndOptions(search.startDateTime)"-->
<!--            value-format="yyyy-MM-dd HH:mm:ss"-->
<!--            style="width: 200px"-->
<!--            size="small"-->
<!--        >-->
<!--        </el-date-picker>-->
        <!-- <el-select
          v-model="search.type"
          placeholder="选择操作类型"
          clearable
          size="small"
        >
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select> -->
        <el-button
            type="primary"
            class="sd-btn-search"
            icon="el-icon-search"
            @click="searchSubmit"
            size="small"
        />
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column
          prop="createTime"
          label="操作时间"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="roleName"
          label="角色"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="studentName"
          label="姓名"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="creatorName"
          label="操作账号"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="typeDes"
          label="操作类型"
          width="180"
          align="center"
      >
      </el-table-column>
      <el-table-column
          prop="content"
          label="操作内容"
          width="180"
          align="center"
      >
      </el-table-column>
    </el-table>
    <div class="sd-pagination-container">
      <el-pagination
          background
          layout="total, prev, pager, next,sizes"
          :total="Number(tableData.total || 0)"
          :current-page.sync="search.pageNumber"
          :page-size.sync="search.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import { getLog } from "@/api/sysConfig";
import {studentRiZhiList1} from "@/api/xueShengRiZhi";

export default {
  name: "sysLog",
  mixins: [TableMixin],
  components: {},
  data() {
    return {
      search: {
        keywords: null,
        startDateTime: "",
        endDateTime: "",

      },
      prefixDeptCode: this.$store.getters.deptCode,
      downloadLoading: false,
      // typeList: [
      //   {
      //     label: "登录",
      //     value: "登录",
      //   },
      //   {
      //     label: "通过",
      //     value: "通过",
      //   },
      //   {
      //     label: "批量审核通过",
      //     value: "批量审核通过",
      //   },
      //   {
      //     label: "驳回-审核通过",
      //     value: "驳回-审核通过",
      //   },
      //   {
      //     label: "驳回-修改信息",
      //     value: "驳回-修改信息",
      //   },
      //   {
      //     label: "调剂",
      //     value: "调剂",
      //   },
      // ],
    };
  },
  created() {},
  methods: {
    async getTableData() {
      console.log(this.search);
      this.tableLoading = true;
      studentRiZhiList1(this.search,this.prefixDeptCode)
          .then((res) => {
            this.tableData = res;
          })
          .finally(() => {
            this.tableLoading = false;
          });
    },
    pickerBeginOptions(endTime) {
      return {
        disabledDate(time) {
          if (endTime) {
            console.log(endTime);
            // 拼接 00:00:00 是为了统一日期从0点开始
            return time.getTime() > new Date(endTime).getTime();
          }
        },
      };
    },
    pickerEndOptions(beginTime) {
      return {
        disabledDate(time) {
          if (beginTime) {
            return time.getTime() < new Date(beginTime).getTime();
          }
        },
      };
    },
    // 导出
    // exportLog() {
    //   this.downloadLoading = true;
    //   this.$download(
    //       "/user-api/log/mng/export",
    //       this.search,
    //       "xls",
    //       "操作日志.xls"
    //   ).then((res) => {
    //     this.$message
    //         .success("下载成功")
    //         .finally(() => (this.downloadLoading = false));
    //   });
    // },
  },
};
</script>

<style scoped lang="scss"></style>
