<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus">添加</el-button>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="账号"
        prop="username"
      ></el-table-column>
      <el-table-column
        prop="defaultPasswordView"
        label="默认密码"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            :underline="false"
            :icon="row.showPasswordView ? 'el-icon-view' : ''"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="姓名"
        prop="nickname"
      ></el-table-column>
      <el-table-column
        align="center"
        label="联系电话"
        prop="mobile"
      ></el-table-column>
      <el-table-column
        align="center"
        label="微信绑定昵称"
        prop="wxname"
        width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        label="审核学校"
        prop="schoolName"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="" label="权限划分" align="center" width="110">
        <template slot-scope="{ row }">
          <el-button type="primary" size="small" @click="permit(row)"
            >分配权限</el-button
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="账号状态" prop="status" width="110">
        <template slot-scope="{ row }">
          <span>{{ switchStatus(row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300">
        <template slot-scope="{ row }">
          <el-link
            type="warning"
            icon="el-icon-edit"
            @click="edit(row)"
            :underline="false"
            style="margin-right: 10px"
            >编辑
          </el-link>
          <el-link
            type="primary"
            icon="el-icon-key"
            @click="resetPassword(row)"
            :underline="false"
            style="margin-right: 10px"
            >重置密码
          </el-link>
          <el-link
            v-if="row.status == 0"
            type="success"
            icon="el-icon-edit"
            @click="changeStatus(row)"
            :underline="false"
            style="margin-right: 10px"
            >启用
          </el-link>
          <el-link
            v-if="row.status == 1"
            type="info"
            icon="el-icon-close"
            @click="changeStatus(row)"
            :underline="false"
            style="margin-right: 10px"
            >禁用
          </el-link>
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="delUser(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 添加，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="auto"
        @keydown.enter="save"
      >
        <el-row>
          <el-col :span="16" :offset="4">
            <el-form-item label="账号" prop="username">
              <el-input
                v-model.trim="form.username"
                placeholder="请输入用户名"
                clearable
                maxlength="32"
                show-word-limit
                size="small"
                style="width: 215px"
              />
            </el-form-item>
            <el-form-item label="姓名" prop="nickname">
              <el-input
                v-model.trim="form.nickname"
                placeholder="请输入真实姓名"
                clearable
                maxlength="32"
                show-word-limit
                size="small"
                style="width: 215px"
              />
            </el-form-item>
            <el-form-item label="电话" prop="mobile">
              <el-input
                v-model.trim="form.mobile"
                placeholder="请输入手机号码"
                clearable
                maxlength="11"
                show-word-limit
                size="small"
                style="width: 215px"
              />
            </el-form-item>
            <el-form-item label="默认密码">
              <div style="color: red; line-height: 20px; padding-top: 9px">
                随机生成数字+字母+特殊符号8—12位组合密码
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 分配权限 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.permit"
      center
      width="600px"
      :close-on-click-modal="false"
    >
      <el-row>
        <el-col :span="16" :offset="3">
          <el-form
            :model="permitForm"
            ref="permitForm"
            :rules="permitRules"
            label-width="90px"
            label-position="right"
          >
            <el-form-item label="账号">
              <span>{{ username }}</span>
            </el-form-item>
            <el-form-item label="选择学段" prop="schoolType">
              <el-select
                v-model="permitForm.schoolType"
                style="width: 100%"
                @change="schoolTypeChange"
                :disabled="isFive"
              >
                <el-option
                  v-for="item in schoolTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择学校" prop="schoolIds">
              <el-select
                v-model="permitForm.schoolIds"
                multiple
                style="width: 100%"
              >
                <el-option
                  v-for="item in schoolList"
                  :key="item.id"
                  :label="item.deptName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('permit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="saveSubmit"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  auditUserList,
  create,
  update,
  resetPwd,
  enableUser,
  disableUser,
  delUser,
  getUserAuditDetail,
  auditSchool,
} from "@/api/user";
import { getDepts } from "@/api/common";
import { mobileValidator } from "@/utils/validator";
import { schoolTypeOptions } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      isFive:this.$store.getters.userInfo.isFive,
      search: {
        pageNumber: 1,
        pageSize: 10,
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
        permit: false,
      },
      form: {
        id: null,
        deptId: this.$store.getters.deptId, // 区县id
        roleId: "3", // 3 区县教育局审核员
        username: null,
        nickname: null,
        mobile: null,
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        nickname: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { validator: mobileValidator, trigger: "blur" },
        ],
      },
      permitForm: {
        userId: null,
        schoolType: null,
        schoolIds: [],
      },
      permitRules: {
        schoolType: [
          { required: true, message: "请选择学段", trigger: "change" },
        ],
        schoolIds: [
          { required: true, message: "请选择学校", trigger: "change" },
        ],
      },
      schoolTypeOptions: schoolTypeOptions,
      schoolList: [],
      username: "",
    };
  },
  computed: {
    switchStatus() {
      return (val) => (val == "1" ? "启用" : "禁用");
    },
  },
  created() {
    if(this.isFive){
      this.form.schoolType="2"
      this.getDepts()
    }
  },
  methods: {
    // 列表
    async getTableData() {
      this.tableLoading = true;
      const data = await auditUserList(this.search);
      for (let i = 0; i < data.records.length; i++) {
        const item = data.records[i];
        item.defaultPasswordView = "********";
        item.showPasswordView = true;
      }
      this.tableData = data;
      this.tableLoading = false;
    },
    changePasswordView(row) {
      if (row.showPasswordView) {
        row.defaultPasswordView = row.defaultPassword;
      } else {
        row.defaultPasswordView = "******";
      }
      row.showPasswordView = !row.showPasswordView;
    },
    // 添加
    add() {
      this.dialogTitle = "添加";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs["form"].resetFields();
      });
    },
    // 编辑
    edit(row) {
      this.dialogTitle = "编辑";
      this.switchModal("addOrEdit", true);
      this.form.id = row.id;
      this.form.username = row.username;
      this.form.nickname = row.nickname;
      this.form.mobile = row.mobile;
    },
    // 分配权限
    permit(row) {
      this.dialogTitle = "分配权限";
      this.switchModal("permit", true);
      this.$nextTick(() => {
        this.$refs.permitForm.resetFields();
        this.permitForm.userId = row.id;
        this.username = row.username;
        this.getUserAuditDetail();
      });
    },
    // 权限详情
    getUserAuditDetail() {
      getUserAuditDetail({ key: this.permitForm.userId }).then((res) => {
        if (res.schoolType) {
          this.permitForm.schoolType = res.schoolType;
        }
        if (res.schoolIds) {
          this.permitForm.schoolIds = res.schoolIds;
        }
        this.getDepts();
      });
    },
    // 选择学段
    schoolTypeChange() {
      this.permitForm.schoolIds = [];
      this.getDepts();
    },
    // 获取学校
    getDepts() {
      if (this.permitForm.schoolType) {
        let params = {
          level: 3,
          period: this.permitForm.schoolType,
          parentId: this.$store.getters.deptId,
        };
        getDepts(params).then((res) => {
          this.schoolList = res;
        });
      }
    },
    // 权限分配 - 提交
    saveSubmit() {
      auditSchool(this.permitForm).then((res) => {
        this.$message.success("操作成功");
        this.switchModal("permit", false);
        this.getTableData();
      });
    },
    // 删除
    delUser(row) {
      this.$confirm(`确定删除【${row.username}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        delUser({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 启用、禁用
    changeStatus(row) {
      this.$confirm(
        `确定${row.status == 1 ? "禁用" : "启用"}【${row.username}】吗？`,
        "提示",
        { type: "warning" }
      ).then(() => {
        this.$notify.success({ message: "操作成功", title: "温馨提示" });
        if (row.status == "1") {
          disableUser({ key: row.id }).then(() => this.getTableData());
        } else {
          enableUser({ key: row.id }).then(() => this.getTableData());
        }
      });
    },
    // 重置密码
    resetPassword(row) {
      this.$confirm(`确定重置【${row.username}】的密码吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$notify.success({ message: "操作成功", title: "温馨提示" });
        resetPwd({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 添加 修改 - 提交
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            create(this.form).then(() => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          } else {
            update(this.form).then(() => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  padding-bottom: 100px;
  min-height: 240px;
}
</style>
