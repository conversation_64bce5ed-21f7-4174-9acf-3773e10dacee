<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button type="primary" icon="el-icon-back" @click="back" size="small"
          >返回</el-button
        >
        <el-button size="small" type="primary" @click="add" icon="el-icon-plus">添加</el-button>
      </div>
      <div class="sd-search">
        <el-form :model="search" :inline="true">
          <el-form-item>
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="搜索小区/村庄名称"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="flex-center school-name">
      {{ schoolName }}
    </div>
    <el-table
      :data="tableData.records"
      border
      stripe
      style="width: 100%"
      v-loading="tableLoading"
    >
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="区域"
        prop="rangeName"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="200px">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            @click="edit(row)"
            style="margin-right: 10px"
            >编辑</el-link
          >
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="del(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="130px">
        <el-form-item label="所属学校">
          <span>{{ schoolName }}</span>
        </el-form-item>
        <el-form-item prop="rangeName" label="招生范围">
          <el-input
            size="small"
            v-model.trim="form.rangeName"
            placeholder="请输入招生范围"
            style="width: 220px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSchoolRangeList,
  createSchoolRange,
  updateSchoolRange,
  deleteSchoolRange,
} from "@/api/setting";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: "",
        // schoolType: "",
        schoolId: "",
        countyId: this.$store.getters.deptId,
        type: 2, // 1区县级别 2学校级别
      },
      form: {
        id: "",
        // schoolType: "",
        schoolId: "",
        rangeName: "",
      },
      rules: {
        rangeName: [
          { required: true, message: "请输入招生范围", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      modal: {
        addOrEdit: false,
      },
      schoolName: "",
    };
  },
  created() {
    this.search.schoolId = this.$route.query.schoolId;
    this.form.schoolId = this.$route.query.schoolId;
    this.schoolName = this.$route.query.schoolName;
  },
  methods: {
    // 列表
    getTableData() {
      this.tableLoading = true;
      getSchoolRangeList(this.search)
        .then((res) => {
          this.tableData = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 添加
    add() {
      this.dialogTitle = "招生范围";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = '';
      });
    },
    // 编辑
    edit(row) {
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form.id = row.id;
        this.form.rangeName = row.rangeName;
      });
    },
    // 删除
    del(row) {
      this.$confirm(`确定删除【区域：${row.rangeName}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        deleteSchoolRange({ key: row.id }).then(() => this.getTableData());
      });
    },
    // 提交
    confirmUpdate() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            createSchoolRange(this.form).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          } else {
            updateSchoolRange(this.form).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getTableData();
            });
          }
        }
      });
    },
    // 返回
    back() {
      this.$router.push("/setting/enrollRange");
    },
  },
};
</script>

<style lang="scss" scoped>
.school-name {
  font-size: 22px;
  padding-bottom: 20px;
}
</style>