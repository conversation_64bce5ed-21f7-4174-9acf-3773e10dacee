<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button icon="el-icon-back" size="small" @click="back"
          >返回</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="addAcct"
          icon="el-icon-plus"
          >添加</el-button
        >
        <!-- 按班级查询 -->
        <el-select
          v-if="shouldShowBanJiField"
          v-model="search.banJi"
          placeholder="按班级查询"
          clearable
          size="small"
          style="width: 120px; margin-left: 10px;"
          @change="handleBanJiSearch"
        >
          <el-option label="全部班级" value="" />
          <el-option
            v-for="item in banJiOptions"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </div>
      <div class="school-title">
        {{ schoolNm }}
      </div>
    </div>
    <el-table :data="tableData" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        fixed="left"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="账号"
        prop="username"
      ></el-table-column>
      <el-table-column
        prop="defaultPasswordView"
        label="默认密码"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            :underline="false"
            :icon="row.showPasswordView ? 'el-icon-view' : ''"
          />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="管理员姓名"
        prop="nickname"
      ></el-table-column>
      <el-table-column
        align="center"
        label="联系电话"
        width="150"
        prop="mobile"
      ></el-table-column>
      <el-table-column
        align="center"
        label="微信绑定昵称"
        width="150"
        prop="wxname"
      ></el-table-column>
      <el-table-column
        align="center"
        label="所属班级"
        width="100"
        prop="banJi"
        v-if="shouldShowBanJiField"
      >
        <template slot-scope="{ row }">
          <span>{{ getBanJiNameById(row.banJi) || '未分配' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="账号状态"
        width="100"
        prop="status"
      >
        <template slot-scope="{ row }">
          <el-link type="info" :underline="false" v-show="row.status == 0"
            >禁用</el-link
          >
          <el-link type="success" :underline="false" v-show="row.status == 1"
            >启用</el-link
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-refresh-left"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="resetPwd(row)"
            >重置密码</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            @click="enable(row)"
            v-if="row.status == 0"
            >启用</el-link
          >
          <el-link
            icon="el-icon-close"
            type="info"
            :underline="false"
            style="margin-right: 10px"
            @click="disable(row)"
            v-else-if="row.status == 1"
            >禁用</el-link
          >
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            style="margin-right: 10px"
            @click="edit(row)"
            >编辑</el-link
          >
          <el-link
            icon="el-icon-delete"
            type="danger"
            :underline="false"
            @click="del(row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog
      :title="addTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        ref="addOrEditForm"
        label-width="100px"
        :rules="formRules"
      >
        <el-form-item prop="username" label="账号">
          <el-input
            size="small"
            v-model.trim="addForm.username"
            placeholder="请输入账号"
          ></el-input>
        </el-form-item>
        <el-form-item prop="nickname" label="管理员姓名">
          <el-input
            size="small"
            v-model.trim="addForm.nickname"
            placeholder="请输入管理员姓名"
          ></el-input>
        </el-form-item>
        <el-form-item prop="mobile" label="联系电话">
          <el-input
            size="small"
            v-model.trim="addForm.mobile"
            placeholder="请输入联系电话"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属班级" prop="banJi" v-if="shouldShowBanJiField">
          <el-select v-model="addForm.banJi" placeholder="请选择班级" clearable style="width: 100%" >
            <el-option v-for="item in banJiOptions" :key="item.id"
                       :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>

      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ModalMixin from "@/mixins/ModalMixin";
import {
  getUserList,
  create,
  update,
  enableUser,
  disableUser,
  resetPwd,
  delUser,
} from "@/api/user";
export default {
  mixins: [ModalMixin],
  data() {
    return {
      schoolNm: "",
      search: {
        deptId: "",
        ancestors: "",
        roleCode: "",
        status: "",
        keywords: "",
        pageNumber: 1,
        pageSize: 10,
        banJi:""
      },
      tableData: [],
      total:0,
      addForm: {
        id: "",
        username: "",
        nickname: "",
        mobile: "",
        defaultPassword: "",
        roleId: 4,
        deptId: "",
        schoolIds: [],
        schoolType: "",
        banJi: "" // 所属班级，存储选中的班级ID
      },
      addTp: "add",
      addTitle: "新增账号",
      modal: {
        addOrEdit: false,
      },
      banJiOptions: [
        { id: 1, label: "1班" },
        { id: 2, label: "2班" },
        { id: 3, label: "3班" },
        { id: 4, label: "4班" },
        { id: 5, label: "5班" },
        { id: 6, label: "6班" },
        { id: 7, label: "7班" },
        { id: 8, label: "8班" },
        { id: 9, label: "9班" },
        { id: 10, label: "10班" },
        { id: 11, label: "11班" },
        { id: 12, label: "12班" },
        { id: 13, label: "13班" },
        { id: 14, label: "14班" },
        { id: 15, label: "15班" },
        { id: 16, label: "16班" },
        { id: 17, label: "17班" },
        { id: 18, label: "18班" },
        { id: 19, label: "19班" },
        { id: 20, label: "20班" }
      ],
    };
  },
  computed: {
    // total() {
    //   return this.tableData && this.tableData.total
    //     ? Number(this.tableData.total)
    //     : 0;
    // },
    shouldShowBanJiField() {
      // 只有当部门代码为130207时才显示所属班级字段
      return this.$store.getters.deptCode === '130207';
    },
    formRules() {
      const baseRules = {
        username: [
          {
            required: true,
            trigger: "blur",
            message: "请输入账号",
          },
        ],
        nickname: [
          {
            required: true,
            trigger: "blur",
            message: "请输入姓名",
          },
        ],
        mobile: [
          {
            required: true,
            trigger: "blur",
            message: "请输入联系电话",
          },
        ],
      };

      // 只有当显示班级字段时才添加班级验证规则
      if (this.shouldShowBanJiField) {
        baseRules.banJi = [
          { required: false, trigger: "change", message: "请选择所属班级" }
        ];
      }

      return baseRules;
    },
  },
  created() {
    let school = this.$route.query;
    this.search.schoolId = school.id;
    this.addForm.deptId = school.id;
    this.addForm.schoolType = school.period;
    this.schoolNm = school.deptName;
    this.getList();
  },
  methods: {
    // 根据班级ID获取班级名称
    getBanJiNameById(banJiId) {
      if (!banJiId) return '';

      // 尝试多种匹配方式
      const banJiItem = this.banJiOptions.find(item => {
        const match = item.id === banJiId ||
                     item.id === String(banJiId) ||
                     String(item.id) === String(banJiId) ||
                     Number(item.id) === Number(banJiId);
        if (match) {
          console.log('找到匹配的班级:', item);
        }
        return match;
      });

      const result = banJiItem ? banJiItem.label : '';
      console.log('getBanJiNameById - 返回结果:', result);
      return result;
    },
    handleSizeChange(size) {
      this.search.pageSize = size
      this.search.pageNumber = 1
      this.getList()
    },
    handleCurrentChange(page) {
      this.search.pageNumber = page
      this.getList()
    },
    // 处理班级查询
    handleBanJiSearch() {
      console.log('按班级查询 - 选中的班级ID:', this.search.banJi);
      this.search.pageNumber = 1; // 重置到第一页
      this.getList(); // 重新获取数据
    },
    // 返
    back() {
      this.$router.go(-1);
    },
    // 添加
    addAcct() {
      this.addTp = "add";
      this.addTitle = "新增账号";
      this.addForm.id = "";
      this.addForm.username = "";
      this.addForm.nickname = "";
      this.addForm.mobile = "";
      this.addForm.banJi = "";
      // 新增时默认为普通管理员权限，选择班级后会自动变为班主任权限
      console.log('新增账号 - 初始化为普通管理员权限');

      this.switchModal("addOrEdit", true);
    },
    // 列表
    getList() {
      this.tableLoading = true;
      getUserList(this.search)
        .then((data) => {
          for (let i = 0; i < data.records.length; i++) {
            const item = data.records[i];
            item.defaultPasswordView = "********";
            item.showPasswordView = true;
          }
          this.tableData = data.records;
          this.total=Number(data.total)
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    changePasswordView(row) {
      if (row.showPasswordView) {
        row.defaultPasswordView = row.defaultPassword;
      } else {
        row.defaultPasswordView = "******";
      }
      row.showPasswordView = !row.showPasswordView;
    },
    // 通过
    enable(row) {
      enableUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 不通过
    disable(row) {
      disableUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 编辑
    edit(row) {
      this.addTp = "edit";
      this.addTitle = "编辑账号";
      this.addForm.id = row.id;
      this.addForm.username = row.username;
      this.addForm.nickname = row.nickname;
      this.addForm.mobile = row.mobile;
      this.addForm.defaultPassword = row.defaultPassword;

      // 如果所属班级有值，进行ID到名称的转换处理
      if (row.banJi) {
        this.addForm.banJi = row.banJi;

      }

      this.switchModal("addOrEdit", true);
    },
    // 删除
    del(row) {
      delUser({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getList();
      });
    },
    // 重置密码
    resetPwd(row) {
      resetPwd({
        key: row.id,
      }).then((res) => {
        this.$message.success("密码已重置");
        this.getList();
      });
    },
    // 确认更新
    confirmUpdate() {
      this.$refs["addOrEditForm"].validate((valid) => {
        if (valid) {
          if (this.addForm.id) {
            update(this.addForm).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getList();
            });
          } else {
            create(this.addForm).then((res) => {
              this.$message.success("操作成功");
              this.switchModal("addOrEdit", false);
              this.getList();
            });
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .school-title {
    font-size: 18px;
    position: absolute;
    left: 50%;
  }
}
</style>
