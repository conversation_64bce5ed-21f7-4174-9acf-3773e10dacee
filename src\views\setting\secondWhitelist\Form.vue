<template>
  <el-form
      :model="form"
      ref="form"
      label-width="100px"
      :rules="rules"
  >
    <el-form-item prop="studentName" label="学生姓名">
      <el-input
          size="small"
          v-model.trim="form.studentName"
          placeholder="请输入学生姓名"
          style="width: 400px"
          maxlength="32"
      ></el-input>
    </el-form-item>
    <el-form-item prop="idCardNumber" label="身份证号">
      <el-input
          size="small"
          v-model.trim="form.idCardNumber"
          placeholder="请输入身份证号"
          style="width: 400px"
          maxlength="18"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script>
import FormMixin from "@/mixins/FormMixin";

export default {
  mixins: [FormMixin],
  data() {
    return {
      form: {
        studentName: '',
        idCardNumber: '',
        id: ''
      }
    }
  },
  mounted() {
    if (this.mode === 'EDIT' && this.data) {
      const {studentName, idCardNumber, id} = this.data
      this.form = {studentName, idCardNumber, id}
    }
  },
    methods: {
    // 暴露 validate 方法
    validate(cb) {
      this.$refs.form.validate(cb)
    }
  }
}

</script>
<style scoped lang="scss">

</style>