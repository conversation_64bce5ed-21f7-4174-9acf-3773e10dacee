<template>
  <div>
    <ul class="setting-list">
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办小学截止添加报名时间</span>
          </div>
          <el-form :model="primaryForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="primaryForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmPrimarySet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办初中截止添加报名时间</span>
          </div>
          <el-form :model="juniorForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="juniorForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmJuniorSet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办小学截止修改报名时间设置</span>
          </div>
          <el-form :model="primarySchoolForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="primarySchoolForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmPrimarySchoolSet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
      <li>
        <el-card class="box-card">
          <div slot="header">
            <span>民办初中截止修改报名时间设置</span>
          </div>
          <el-form :model="juniorSchoolForm" label-width="100px">
            <el-form-item prop="endTime" label="截止时间">
              <el-date-picker
                  size="small"
                  v-model="juniorSchoolForm.endTime"
                  type="datetime"
                  :editable="false"
                  :clearable="true"
                  placeholder="请选择截止时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="flex-end">
            <el-button
                type="success"
                size="small"
                @click="confirmJuniorSchoolSet()"
            >确定</el-button
            >
          </div>
        </el-card>
      </li>
    </ul>
  </div>
</template>

<script>
import { getTimePrivate,updateTimePrivate } from "@/api/setting";
export default {
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      primaryForm: {
        id: '3',
        endTime: ''
      },
      juniorForm: {
        id: '4',
        endTime: ''
      },
      primarySchoolForm: {
        id: '5',
        endTime: ''
      },
      juniorSchoolForm: {
        id: '6',
        endTime: ''
      },
    }
  },
  created() {
    this.getDeadline(3);
    this.getDeadline(4);
    this.getDeadline(5);
    this.getDeadline(6);
  },
  methods: {
    // 获取
    getDeadline(row) {
      getTimePrivate({key: row}, this.prefixDeptCode).then((res) => {
       if(row==3){
         this.primaryForm=res
       }
        if(row==4){
          this.juniorForm=res
        }
        if(row==5){
          this.primarySchoolForm=res
        }
        if(row==6){
          this.juniorSchoolForm=res
        }
      });
    },
    // 小学 - 确认
    confirmPrimarySet() {
      updateTimePrivate(this.primaryForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline(3)
      });
    },
    // 初中 - 确认
    confirmJuniorSet() {
      updateTimePrivate(this.juniorForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline(4)
      });
    },
    // 小学学校截止添加报名时间 - 确认
    confirmPrimarySchoolSet() {
      updateTimePrivate(this.primarySchoolForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline(5)
      });
    },
    // 初中学校截止添加报名时间 - 确认
    confirmJuniorSchoolSet() {
      updateTimePrivate(this.juniorSchoolForm, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.getDeadline(6)
      });
    },
  },
};
</script>

<style scoped lang="scss">
.setting-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;

  & > li {
    flex: 0 0 33.3333%;
    padding: 10px 10px;
    box-sizing: border-box;
  }
}
</style>
