<template>
	<div>
		<ul class="transfer-switch">
			<li v-for="item, idx in list">
				<el-card class="box-card">
				  <div slot="header">
				    <span>{{ item.des }}</span>
				  </div>
				  <el-form :model="item" label-width="100px">
				    <el-radio-group v-model="item.status">
							<template v-if="item.id == 1">
								<el-radio label="0">开启</el-radio>
								<el-radio label="1">关闭</el-radio>
							</template>
							<template v-else>
								<el-radio label="0">是</el-radio>
								<el-radio label="1">否</el-radio>
							</template>
						</el-radio-group>
				  </el-form>
				  <div class="flex-end">
				    <el-button
				      type="success"
				      size="small"
				      @click="confirmSet(item)"
				      >确定</el-button
				    >
				  </div>
				</el-card>
			</li>
		</ul>
	</div>
</template>

<script>
import { getTransferSwitch, setTransferSwitch } from "@/api/sysConfig"
export default {
	data() {
		return {
			prefixDeptCode: this.$store.getters.deptCode,
			list: []
		}
	},
	created() {
		this.getSwitch()
	},
	methods: {
		// 获取开关
		getSwitch() {
			getTransferSwitch(this.prefixDeptCode).then(res => {
				this.list = res
			})
		},
		// 设置
		confirmSet(item) {
			setTransferSwitch(item, this.prefixDeptCode).then(res => {
				this.$message.success('开关设置成功')
			})
		}
	}
}
</script>

<style scoped lang="scss">
.transfer-switch {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	
	& > li {
	  flex: 0 0 33.3333%;
	  padding: 10px 10px;
	  box-sizing: border-box;
	}
}
</style>