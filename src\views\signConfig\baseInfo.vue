<!-- 报名填写字段 - 基础信息 -->
<template>
  <div>
    <el-tabs tab-position="left" :stretch="true">
      <el-tab-pane label="基础信息">
        <el-table :data="tableDataJc" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="监护人信息">
        <el-table :data="tableDataJhr" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="双胞胎信息">
        <el-table :data="tableDataSbt" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="户口信息">
        <el-table :data="tableDataHk" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="经商信息">
        <el-table :data="tableDataJs" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="务工信息">
        <el-table :data="tableDataWg" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="居住证信息">
        <el-table :data="tableDataJzz" border stripe>
          <el-table-column label="字段" prop="field" align="center" />
          <el-table-column label="输入项" prop="inputItem" align="center" />
          <el-table-column label="条件限制" prop="limit" align="center" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableDataJc: [
        {
          field: "报名编号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "学生姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "学生身份证号",
          inputItem: "手动输入、身份证识别",
          limit: "判断身份证号真实有效性",
        },
        {
          field: "学生出生日期",
          inputItem: "下拉框、身份证识别",
          limit: "年月日/根据身份证号自动识别",
        },
        {
          field: "学生性别",
          inputItem: "下拉框、身份证识别",
          limit: "男女/根据身份证号自动识别",
        },
        {
          field: "学生民族",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "是否残疾儿童",
          inputItem: "下拉框",
          limit: "是、否",
        },
        {
          field: "是否插班生报名",
          inputItem: "下拉框",
          limit: "是、否",
        },
        {
          field: "插班生报名年级",
          inputItem: "下拉框",
          limit: "可选择一年级至初三",
        },
        {
          field: "毕业小学",
          inputItem: "手动输入、下拉框",
          limit: "下拉框取值教育局设置的小学学校名单",
        },
        {
          field: "毕业幼儿园",
          inputItem: "手动输入、下拉框",
          limit: "下拉框取值教育局设置的幼儿园名单",
        },
        {
          field: "学籍号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "户籍所在小区/村庄",
          inputItem: "手动输入、下拉框",
          limit: "下拉框取值教育局设置的学校招生范围",
        },
        {
          field: "现家庭住址",
          inputItem: "手动输入、下拉框",
          limit: "下拉框取值教育局设置的学校招生范围",
        },
        {
          field: "户口所在地",
          inputItem: "手动输入",
          limit: "",
        },
      ],
      tableDataJhr: [
        {
          field: "监护人1姓名",
          inputItem: "",
          limit: "",
        },
        {
          field: "性别",
          inputItem: "",
          limit: "",
        },
        {
          field: "民族",
          inputItem: "",
          limit: "",
        },
        {
          field: "出生",
          inputItem: "",
          limit: "",
        },
        {
          field: "现住址",
          inputItem: "",
          limit: "",
        },
        {
          field: "公民身份证号",
          inputItem: "",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "",
          limit: "",
        },
        {
          field: "监护人1手机号",
          inputItem: "",
          limit: "",
        },
        {
          field: "监护人1户口所在地",
          inputItem: "",
          limit: "",
        },
        {
          field: "监护人2姓名",
          inputItem: "",
          limit: "",
        },
        {
          field: "性别",
          inputItem: "",
          limit: "",
        },
        {
          field: "民族",
          inputItem: "",
          limit: "",
        },
        {
          field: "出生",
          inputItem: "",
          limit: "",
        },
        {
          field: "现住址",
          inputItem: "",
          limit: "",
        },
        {
          field: "公民身份证号",
          inputItem: "",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "",
          limit: "",
        },
        {
          field: "监护人2手机号",
          inputItem: "",
          limit: "",
        },
        {
          field: "监护人2户口所在地",
          inputItem: "",
          limit: "",
        },
      ],
      tableDataSbt: [
        {
          field: "姓名",
          inputItem: "",
          limit: "",
        },
        {
          field: "性别",
          inputItem: "筛选",
          limit: "男/女",
        },
        {
          field: "民族",
          inputItem: "",
          limit: "",
        },
        {
          field: "出生",
          inputItem: "",
          limit: "",
        },
        {
          field: "公民身份证号",
          inputItem: "",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "弟弟/妹妹/哥哥/姐姐",
        },
      ],
      tableDataHk: [
        {
          field: "户主姓名（与学生所在同一户口本上户主姓名）",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "户主身份证号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "户号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "户籍地址",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
      ],
      tableDataJs: [
        {
          field: "名称",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "经营者姓名/法定代表人",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "统一社会信用代码",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "详细地址",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "注册日期",
          inputItem: "下拉框",
          limit: "年月日",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
      ],
      tableDataWg: [
        {
          field: "劳动合同编号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "单位名称",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "详细地址",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "乙方（职工）姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "合同签订日期",
          inputItem: "下拉框",
          limit: "年月日",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/祖父母/外祖父母/本人",
        },
      ],
      tableDataJzz: [
        {
          field: "居住证编号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "居住证姓名",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "身份证号",
          inputItem: "手动输入",
          limit: "",
        },
        {
          field: "居住证地址",
          inputItem: "手动输入/下拉选择",
          limit: "下拉选择取值教育局设置学校招生范围",
        },
        {
          field: "与学生关系",
          inputItem: "筛选",
          limit: "父子/父女/母子/母女/本人",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style>
</style>