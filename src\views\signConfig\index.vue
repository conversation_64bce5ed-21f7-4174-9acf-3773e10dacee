<template>
  <div>
    <el-tabs
      tab-position="left"
      :stretch="true"
      type="border-card"
      v-model="prefixDeptCode"
      @tab-click="getList"
    >
      <el-tab-pane
        v-for="item in deptOptions"
        :label="item.deptName"
        :name="item.deptCode"
        :key="item.id"
      >
        <div class="sd-option-container">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            @click="add"
            size="small"
            v-if="addFlag"
            >添加</el-button
          >
        </div>
        <el-table
          :data="tableData.records"
          border
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            prop="countyName"
            label="所属区县"
            align="center"
            width="120px"
          ></el-table-column>
          <el-table-column prop="systemName" label="系统名称" align="center">
            <template slot-scope="scope">
              <span @click="edit(scope.row.id)" style="cursor: pointer">
                {{ scope.row.systemName }}
                <el-link type="info" :underline="false" icon="el-icon-edit">
                </el-link>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="entrance"
            label="报名入口"
            align="center"
            width="180px"
          ></el-table-column>
          <el-table-column
            prop="field"
            label="报名学段"
            align="center"
            width="180px"
          ></el-table-column>
          <el-table-column
            prop="categoryOfRegistration"
            label="报名类别"
            align="center"
            width="400"
          >
            <template slot-scope="{ row }">
              <ul class="category">
                <li
                  v-for="(item, index) in switchCategory(
                    row.categoryOfRegistration
                  )"
                  :key="index"
                >
                  {{ item }}
                </li>
              </ul>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="200"
            fixed="right"
            align="center"
          >
            <template>
              <el-link
                type="primary"
                @click="signTypeShow()"
                :underline="false"
                icon="el-icon-s-tools"
                >类别设置 </el-link
              >&ensp;
              <el-link
                type="success"
                @click="signFieldShow()"
                :underline="false"
                icon="el-icon-s-operation"
                >字段设置
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加、修改 -->
    <el-dialog
      title="系统设置"
      :visible.sync="modal.addOrEdit"
      center
      :close-on-click-modal="false"
      width="950px"
    >
      <el-form ref="addForm" :model="addForm" :rules="addRules">
        <el-card>
          <el-row>
            <el-form-item label="所属区县：">
              <span class="title">{{ addForm.countyName }}</span>
            </el-form-item>
            <el-form-item label="系统名称：" prop="systemName">
              <el-input
                v-model.trim="addForm.systemName"
                placeholder="请输入系统名称"
                clearable
                maxlength="32"
                show-word-limit
                size="small"
                style="width: 400px"
              />
            </el-form-item>
            <el-form-item label="报名渠道：" prop="channelList">
              <el-checkbox-group v-model="addForm.channelList" size="small">
                <el-checkbox
                  v-for="item in channelList"
                  :label="item.label"
                  :key="item.key"
                ></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-row>
        </el-card>
        <el-card>
          <div class="title">报名年龄段设置</div>
          <el-row>
            <div class="sub-title">幼儿园报名年龄区间设置：</div>
            <div flex>
              <el-form-item label="最小年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.kindergartenMinAge"
                  type="date"
                  placeholder="选择日期"

                  clearable
                  @input="handleDate('kindergartenMinAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
<!--                  :picker-options="pickerBeginOptions(addForm.kindergartenMaxAge)"-->
                </el-date-picker>
              </el-form-item>
              <el-form-item label="最大年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.kindergartenMaxAge"
                  type="date"
                  placeholder="选择日期"
                  clearable
                  @input="handleDate('kindergartenMaxAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
<!--                  :picker-options="pickerEndOptions(addForm.kindergartenMinAge)"-->
                </el-date-picker>
              </el-form-item>
            </div>
          </el-row>
          <el-row>
            <div class="sub-title">小学报名年龄区间设置：</div>
            <div flex>
              <el-form-item label="最小年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.primaryMinAge"
                  type="date"
                  placeholder="选择日期"

                  clearable
                  @input="handleDate('primaryMinAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
<!--                  :picker-options="pickerBeginOptionsPri(addForm.primaryMaxAge)"-->
                </el-date-picker>
              </el-form-item>
              <el-form-item label="最大年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.primaryMaxAge"
                  type="date"
                  placeholder="选择日期"

                  clearable
                  @input="handleDate('primaryMaxAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
<!--                :picker-options="pickerEndOptionsPri(addForm.primaryMinAge)"-->
              </el-form-item>
            </div>
          </el-row>
          <el-row>
            <div class="sub-title">初中报名年龄区间设置：</div>
            <div flex>
              <el-form-item label="最小年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.juniorMinAge"
                  type="date"
                  placeholder="选择日期"

                  clearable
                  @input="handleDate('juniorMinAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
<!--                  :picker-options="pickerBeginOptions(addForm.juniorMaxAge)"-->
                </el-date-picker>
              </el-form-item>
              <el-form-item label="最大年龄限制:" label-width="140px">
                <el-date-picker
                  v-model="addForm.juniorMaxAge"
                  type="date"
                  placeholder="选择日期"

                  clearable
                  @input="handleDate('juniorMaxAge')"
                  size="small"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
<!--                :picker-options="pickerEndOptions(addForm.juniorMinAge)"-->
              </el-form-item>
            </div>
          </el-row>
        </el-card>
        <el-card>
          <div class="title">报名设置</div>
          <el-row>
            <el-col :span="14">
              <el-form-item prop="rejectedTypeList" label="驳回类别:">
                <el-checkbox-group
                  v-model="addForm.rejectedTypeList"
                  size="small"
                >
                  <el-checkbox
                    v-for="item in rejectedTypeList"
                    :label="item.label"
                    :key="item.key"
                  ></el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <div flex>
              <div class="sub-title1">报名志愿数量设置：</div>
              <div flex>
                <el-checkbox
                  v-model="addForm.kindergartenVolunteer"
                  :true-label="1"
                  :false-label="0"
                  style="padding-top: 9px; padding-right: 5px"
                  @change="kindergartenVolunteerChange"
                >
                </el-checkbox>
                <el-form-item
                  label="幼儿园:"
                  style="width: 220px"
                  :prop="
                    addForm.kindergartenVolunteer == 1
                      ? 'kindergartenVolunteerNum'
                      : 'empty'
                  "
                >
                  <el-input
                    v-model="addForm.kindergartenVolunteerNum"
                    placeholder="输入数量"
                    size="small"
                    type="number"
                    min="0"
                    style="width: 100px"
                    :disabled="!addForm.kindergartenVolunteer"
                    @change="kindergartenVolunteerNumChange"
                  ></el-input>
                  个
                </el-form-item>
              </div>
              <div flex>
                <el-checkbox
                  v-model="addForm.primaryVolunteer"
                  :true-label="1"
                  :false-label="0"
                  style="padding-top: 9px; padding-right: 5px"
                  @change="addForm.primaryVolunteerNum = ''"
                >
                </el-checkbox>
                <el-form-item
                  label="小学:"
                  style="width: 220px"
                  :prop="
                    addForm.primaryVolunteer == 1
                      ? 'primaryVolunteerNum'
                      : 'empty'
                  "
                >
                  <el-input
                    v-model="addForm.primaryVolunteerNum"
                    placeholder="输入数量"
                    size="small"
                    type="number"
                    min="0"
                    style="width: 100px"
                    :disabled="!addForm.primaryVolunteer"
                    @change="primaryVolunteerNumChange"
                  ></el-input>
                  个
                </el-form-item>
              </div>
              <div flex>
                <el-checkbox
                  v-model="addForm.juniorVolunteer"
                  :true-label="1"
                  :false-label="0"
                  style="padding-top: 9px; padding-right: 5px"
                  @change="addForm.juniorVolunteerNum = ''"
                >
                </el-checkbox>
                <el-form-item
                  label="初中:"
                  style="width: 220px"
                  :prop="
                    addForm.juniorVolunteer == 1
                      ? 'juniorVolunteerNum'
                      : 'empty'
                  "
                >
                  <el-input
                    v-model="addForm.juniorVolunteerNum"
                    placeholder="输入数量"
                    size="small"
                    type="number"
                    min="0"
                    style="width: 100px"
                    :disabled="!addForm.juniorVolunteer"
                    @change="juniorVolunteerNumChange"
                  ></el-input>
                  个
                </el-form-item>
              </div>
            </div>
          </el-row>
          <el-row>
            <el-form-item prop="isSchoolCheckin" label="是否学校点击报到：">
              <el-radio-group v-model="addForm.isSchoolCheckin">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              prop="isSchoolNotice"
              label="是否学校发送录取通知书："
            >
              <el-radio-group v-model="addForm.isSchoolNotice">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item prop="isMultiple" label="多胞胎绑定开关设置：">
              <el-radio-group v-model="addForm.isMultiple">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              prop="registrationModification"
              label="报名期间报名信息可修改次数设置："
            >
              <el-input
                v-model="addForm.registrationModification"
                placeholder="输入数量"
                type="number"
                :min="0"
                size="small"
                style="width: 110px"
              ></el-input>
              次
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              prop="wechatModification"
              label="一个微信可以报名数量设置："
            >
              <el-input
                v-model="addForm.wechatModification"
                placeholder="输入数量"
                type="number"
                :min="0"
                size="small"
                style="width: 110px"
              ></el-input>
              名
            </el-form-item>
          </el-row>
        </el-card>
      </el-form>
      <div class="flex-center">
        <el-button
          type="default"
          @click="switchModal('addOrEdit', false)"
          size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmit"
          size="small"
          :loading="submitLoading"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <!-- 报名类别设置 -->
    <el-dialog
      title="类别设置"
      :visible.sync="modal.signType"
      center
      :close-on-click-modal="false"
    >
      <div style="margin-bottom: 15px">
        <div style="padding-bottom: 10px; color: #000">
          同步类别及字段设置：
        </div>
        <el-button type="primary" @click="handleSync(1)" size="small"
          >乡镇初中同步乡镇小学</el-button
        >
        <el-button type="warning" @click="handleSync(2)" size="small"
          >城区初中同步城区小学</el-button
        >
        <el-button type="warning" @click="handleSync(3)" size="small"
          >城区小学同步乡镇小学</el-button
        >
        <el-button type="warning" @click="handleSync(4)" size="small"
          >城区初中同步乡镇初中</el-button
        >
      </div>
      <el-tree
        :data="treeData"
        show-checkbox
        :default-checked-keys="treeSelectedNodes"
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        :indent="30"
      >
      </el-tree>
      <div class="flex-center sd-m-t-15">
        <el-button
          type="default"
          @click="switchModal('signType', false)"
          size="small"
          >关 闭</el-button
        >
        <el-button
          type="primary"
          @click="handleTypeSubmit"
          size="small"
          :loading="submitTypeLoading"
          >保 存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getSignConfigList,
  signSystemCreate,
  signSystemUpdate,
  signSystemInfo,
  getSystemSetUpTree,
  systemSetupSave,
  getSystemSetupNodes,
  syncEnrollField,
} from "@/api/signConfig";
import { getDepts } from "@/api/common";
export default {
  name: "signConfig",
  mixins: [TableMixin, ModalMixin],
  components: {},
  data() {
    return {
      prefixDeptCode: "0", // 当前选项卡区县
      search: {
        // countyId: "",
      },
      modal: {
        addOrEdit: false,
        signType: false,
      },
      addForm: {
        id: null,
        countyId: "",
        countyName: "",
        systemName: "",
        channelList: [],
        registrationChannel: "",

        kindergartenMinAge: "",
        kindergartenMaxAge: "",
        primaryMinAge: "",
        primaryMaxAge: "",
        juniorMinAge: "",
        juniorMaxAge: "",

        rejectedTypeList: [],
        rejectedType: "",
        kindergartenVolunteer: 0,
        kindergartenVolunteerNum: "",
        primaryVolunteer: 0,
        primaryVolunteerNum: "",
        juniorVolunteer: 0,
        juniorVolunteerNum: "",

        isSchoolCheckin: "",
        isSchoolNotice: "",
        isMultiple: "",
        registrationModification: "2",
        wechatModification: "1",
      },
      deptOptions: [],
      channelList: [
        {
          label: "官网",
          key: "c1",
        },
        {
          label: "公众号",
          key: "c2",
        },
      ],
      rejectedTypeList: [
        {
          label: "驳回-修改信息",
          key: "r1",
        },
        {
          label: "驳回-重新报名",
          key: "r2",
        },
        {
          label: "驳回-不可重报",
          key: "r3",
        },
      ],
      addRules: {
        countyId: [
          { required: true, message: "请选择区县", trigger: "change" },
        ],
        systemName: [
          { required: true, message: "请输入系统名称", trigger: "blur" },
        ],
        channelList: [
          { required: true, message: "请选择渠道", trigger: "change" },
        ],
        kindergartenVolunteerNum: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
        primaryVolunteerNum: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
        juniorVolunteerNum: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
        rejectedTypeList: [
          { required: true, message: "请选择类别", trigger: "change" },
        ],
        isSchoolCheckin: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
        isSchoolNotice: [
          { required: true, message: "请选择", trigger: "blur" },
        ],
        isMultiple: [{ required: true, message: "请选择", trigger: "blur" }],
        registrationModification: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
        wechatModification: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
      },
      treeData: [],
      treeObj: {
        children: [],
      },
      treeSelectedNodes: [],
      defaultProps: {
        children: "children",
        label: "name",
      },
      submitLoading: false,
      submitTypeLoading: false,
      county: "",
      addFlag: true,
    };
  },
  computed: {
    switchCategory() {
      return (val) => (val ? val.slice(0, -1).split(";,") : []);
    },
  },
  created() {},
  methods: {
    // 初次查询
    async getTableData() {
      if (this.prefixDeptCode != "0") {
        this.getList();
      } else {
        this.deptOptions = await getDepts({ level: 2 });
        if (this.$route.query.code) {
          this.prefixDeptCode = this.$route.query.code;
        } else {
          this.prefixDeptCode = this.deptOptions[0].deptCode;
        }
        this.getList();
      }
    },
    // 根据区县切换，查询报名设置列表
    getList() {
      // console.log(this.prefixDeptCode);
      this.tableData = [];
      this.tableLoading = true;
      getSignConfigList(this.search, this.prefixDeptCode)
        .then((res) => {
          this.tableData = res;
          this.county = this.tableData.records[0].countyName;
          this.addFlag = false;
        })
        .catch((err) => {
          if (err.status == 404) {
            this.addFlag = false;
          } else if (err.status == 701) {
            this.addFlag = true;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 报名系统 - 添加
    add() {
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs["addForm"].resetFields();
        this.addForm = {
          id: null,
          countyId: "",
          countyName: "",
          systemName: "",
          channelList: [],
          registrationChannel: "",

          kindergartenMinAge: "",
          kindergartenMaxAge: "",
          primaryMinAge: "",
          primaryMaxAge: "",
          juniorMinAge: "",
          juniorMaxAge: "",

          rejectedTypeList: [],
          rejectedType: "",
          kindergartenVolunteer: 0,
          kindergartenVolunteerNum: "",
          primaryVolunteer: 0,
          primaryVolunteerNum: "",
          juniorVolunteer: 0,
          juniorVolunteerNum: "",

          isSchoolCheckin: 1,
          isSchoolNotice: 1,
          isMultiple: "",
          registrationModification: "2",
          wechatModification: "1",
        };
        this.deptOptions.forEach((item) => {
          if (item.deptCode == this.prefixDeptCode) {
            this.addForm.countyId = item.id;
            this.addForm.countyName = item.deptName;
          }
        });
      });
    },
    // 报名系统 - 修改
    edit(id) {
      signSystemInfo({ key: id }, this.prefixDeptCode).then((res) => {
        this.addForm.id = res.id;
        this.addForm.countyId = res.countyId;
        this.addForm.countyName = res.countyName;
        this.addForm.systemName = res.systemName;
        this.addForm.registrationChannel = res.registrationChannel;
        this.addForm.kindergartenMinAge = res.kindergartenMinAge;
        this.addForm.kindergartenMaxAge = res.kindergartenMaxAge;
        this.addForm.primaryMinAge = res.primaryMinAge;
        this.addForm.primaryMaxAge = res.primaryMaxAge;
        this.addForm.juniorMinAge = res.juniorMinAge;
        this.addForm.juniorMaxAge = res.juniorMaxAge;
        this.addForm.rejectedType = res.rejectedType;
        this.addForm.kindergartenVolunteer = res.kindergartenVolunteer;
        this.addForm.kindergartenVolunteerNum = res.kindergartenVolunteerNum;
        this.addForm.primaryVolunteer = res.primaryVolunteer;
        this.addForm.primaryVolunteerNum = res.primaryVolunteerNum;
        this.addForm.juniorVolunteer = res.juniorVolunteer;
        this.addForm.juniorVolunteerNum = res.juniorVolunteerNum;
        this.addForm.isSchoolCheckin = res.isSchoolCheckin;
        this.addForm.isSchoolNotice = res.isSchoolNotice;
        this.addForm.isMultiple = res.isMultiple;
        this.addForm.registrationModification = res.registrationModification;
        this.addForm.wechatModification = res.wechatModification;
        this.addForm.channelList = [];
        if (this.addForm.registrationChannel.includes("A")) {
          this.addForm.channelList.push("官网");
        }
        if (this.addForm.registrationChannel.includes("B")) {
          this.addForm.channelList.push("公众号");
        }
        this.addForm.rejectedTypeList = [];
        if (this.addForm.rejectedType.includes("A")) {
          this.addForm.rejectedTypeList.push("驳回-修改信息");
        }
        if (this.addForm.rejectedType.includes("B")) {
          this.addForm.rejectedTypeList.push("驳回-重新报名");
        }
        if (this.addForm.rejectedType.includes("C")) {
          this.addForm.rejectedTypeList.push("驳回-不可重报");
        }
      });
      this.switchModal("addOrEdit", true);
    },
    // 最小年龄限制
    // pickerBeginOptions(endTime) {
    //   return {
    //     disabledDate(time) {
    //       if (endTime) {
    //         return time.getTime() < new Date(endTime).getTime() - 8.64e7;
    //       }
    //     },
    //   };
    // },
    // // 最大年龄限制
    // pickerEndOptions(beginTime) {
    //   return {
    //     disabledDate(time) {
    //       if (beginTime) {
    //         return time.getTime() > new Date(beginTime).getTime() - 8.64e7;
    //       }
    //     },
    //   };
    // },
    // // 小学最小年龄限制
    // pickerBeginOptionsPri() {
    //   return {
    //     disabledDate(time) {
    //       let primaryMinDate = new Date(
    //         new Date().getFullYear() - 6 + "-08-31"
    //       ).getTime();
    //       return time.getTime() > primaryMinDate;
    //     },
    //   };
    // },
    // // 小学最大年龄限制
    // pickerEndOptionsPri() {
    //   return {
    //     disabledDate(time) {
    //       let primaryMaxDate = new Date(
    //         new Date().getFullYear() - 7 + "-08-31"
    //       ).getTime();
    //       return time.getTime() < primaryMaxDate;
    //     },
    //   };
    // },
    kindergartenVolunteerChange() {
      this.addForm.kindergartenVolunteerNum = "";
      this.kindergartenVolunteerNumChange();
    },
    primaryVolunteerChange() {
      this.addForm.primaryVolunteerNum = "";
      this.primaryVolunteerNumChange();
    },
    juniorVolunteerChange() {
      this.addForm.juniorVolunteerNum = "";
      this.juniorVolunteerNumChange();
    },
    kindergartenVolunteerNumChange() {
      this.$refs["addForm"].clearValidate("kindergartenVolunteerNum");
    },
    primaryVolunteerNumChange() {
      this.$refs["addForm"].clearValidate("primaryVolunteerNum");
    },
    juniorVolunteerNumChange() {
      this.$refs["addForm"].clearValidate("juniorVolunteerNum");
    },
    // 清空日期选择
    handleDate(key) {
      if (this.addForm[key] == null) {
        this.addForm[key] = "";
      }
    },
    // 报名系统添加 - 提交
    handleSubmit() {
      this.$refs["addForm"].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          let _channelList = [];
          this.addForm.channelList.forEach((item) => {
            _channelList.push(
              item == "官网" ? "A" : item == "公众号" ? "B" : ""
            );
          });
          this.addForm.registrationChannel = _channelList.sort().join("");
          let _rejectedTypeList = [];
          this.addForm.rejectedTypeList.forEach((item) => {
            _rejectedTypeList.push(
              item == "驳回-修改信息"
                ? "A"
                : item == "驳回-重新报名"
                ? "B"
                : item == "驳回-不可重报"
                ? "C"
                : ""
            );
          });
          this.addForm.rejectedType = _rejectedTypeList.sort().join("");

          this.addForm.registrationModification = parseInt(
            this.addForm.registrationModification
          );
          this.addForm.wechatModification = parseInt(
            this.addForm.wechatModification
          );

          // 添加
          if (!this.addForm.id) {
            signSystemCreate(this.addForm, this.prefixDeptCode)
              .then((res) => {
                this.submitLoading = false;
                this.switchModal("addOrEdit", false);
                this.$message.success("操作成功");
                this.getList();
              })
              .catch(() => {
                this.submitLoading = false;
              });
          } else {
            // 修改
            signSystemUpdate(this.addForm, this.prefixDeptCode)
              .then((res) => {
                this.submitLoading = false;
                this.switchModal("addOrEdit", false);
                this.$message.success("操作成功");
                this.getList();
              })
              .catch(() => {
                this.submitLoading = false;
              });
          }
        }
      });
    },
    // 报名类别设置
    signTypeShow() {
      this.treeData = [];
      this.treeObj.children = [];
      this.treeSelectedNodes = [];
      this.getSystemSetUpTree();
      this.getSystemSetupNodes();
      this.switchModal("signType", true);
    },
    // 获取类别设置树形节点
    getSystemSetUpTree() {
      getSystemSetUpTree({}, this.prefixDeptCode).then((res) => {
        this.treeData = res;
      });
    },
    // 获取类别设置树形已选节点
    getSystemSetupNodes() {
      getSystemSetupNodes({}, this.prefixDeptCode).then((res) => {
        this.treeSelectedNodes = res;
      });
    },
    // 类别设置提交
    handleTypeSubmit() {
      // console.log(this.$refs.tree.getCheckedNodes());
      let nodeList = this.$refs.tree.getCheckedNodes();
      if (nodeList.length == 0) {
        this.$message.warning("请选择报名类别");
        return false;
      } else {
        this.submitTypeLoading = true;
        this.treeObj.children = nodeList
          .filter((item) => item.children.length == 0)
          .map((item) => item.id);
        systemSetupSave(this.treeObj, this.prefixDeptCode)
          .then((res) => {
            this.submitTypeLoading = false;
            this.$message.success("操作成功");
            this.getList();
          })
          .catch(() => {
            this.submitTypeLoading = false;
          });
      }
    },
    // 报名字段设置, 传区县deptCode
    signFieldShow() {
      this.$router.push({
        path: "/signConfig/signFieldSet",
        query: { code: this.prefixDeptCode, county: this.county },
      });
    },
    // 同步报名类别及字段设置
    handleSync(type) {
      syncEnrollField({ type }, this.prefixDeptCode).then((res) => {
        this.$message.success("同步成功");
        this.treeData = [];
        this.treeObj.children = [];
        this.treeSelectedNodes = [];
        this.getSystemSetUpTree();
        this.getSystemSetupNodes();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-tree {
  height: 600px;
  overflow-y: scroll;
}
.el-card {
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 15px;
}
.sub-title1 {
  color: #606266;
  padding-top: 10px;
}
.el-tabs {
  height: calc(100vh - var(--header-height) - 35px);
  .category {
    float: left;
    text-align: left;
    padding-left: 10px;
  }
}
</style>
