<template>
  <div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        prop="module"
        label="对应模块"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="document"
        label="对应文件"
        align="center"
      ></el-table-column>
      <el-table-column prop="name" label="导出表格名称" align="center">
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="auto"
            height="400"
            trigger="click"
          >
            <div>
              <el-input
                v-model="tableNameTemp.name"
                placeholde="请输入名称"
              ></el-input>
              <div class="flex-center">
                <div class="flex-center sd-m-t-15">
                  <el-button type="plain" @click="closeAllPopovers" size="small"
                    >取 消</el-button
                  >
                  <el-button
                    type="primary"
                    @click="saveName(scope.$index)"
                    size="small"
                    >保 存</el-button
                  >
                </div>
              </div>
            </div>
            <span
              @click="editTableName(scope.row)"
              style="cursor: pointer"
              slot="reference"
            >
              {{ scope.row.name }}
              <el-link type="info" :underline="false" icon="el-icon-edit">
              </el-link>
            </span>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="{ row }">
          <el-link
            type="primary"
            @click="setShow(row)"
            :underline="false"
            icon="el-icon-s-tools"
            style="margin-right: 10px"
            >设置</el-link
          >
          <el-link
            type="success"
            @click="detailShow(row)"
            :underline="false"
            icon="el-icon-view"
            >查看</el-link
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 导出表格设置 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.set"
      center
      :close-on-click-modal="false"
    >
      <div class="tree-box" v-loading="treeLoading">
        <el-tree
          :data="treeData"
          show-checkbox
          :default-checked-keys="treeSelectedNodes"
          node-key="id"
          ref="tree"
          highlight-current
          :props="defaultProps"
          :indent="30"
        >
          <span class="custom-tree-node" slot-scope="{ data }">
            <span
              >{{ data.fieldName ? data.fieldName : data.infoName }}
              {{ data.fieldEnglish }}</span
            >
          </span>
        </el-tree>
      </div>
      <div class="flex-center sd-m-t-15">
        <el-button
          type="default"
          @click="switchModal('set', false)"
          size="small"
          >关 闭</el-button
        >
        <el-button
          type="primary"
          @click="handleSetSubmit"
          size="small"
          :loading="submitLoading"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <!-- 导出表格详情 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.detail"
      center
      :close-on-click-modal="false"
    >
      <div class="tree-box" v-loading="treeLoading">
        <el-tree
          :data="treeData"
          node-key="id"
          ref="treeDetail"
          highlight-current
          :props="defaultProps"
          :indent="30"
          default-expand-all
        >
          <span class="custom-tree-node" slot-scope="{ data }">
            <span
              >{{ data.fieldName ? data.fieldName : data.infoName }}
              {{ data.fieldEnglish }}</span
            >
          </span>
        </el-tree>
      </div>
      <div class="flex-center sd-m-t-15">
        <el-button
          type="default"
          @click="switchModal('detail', false)"
          size="small"
          >关 闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  getExcelNameList,
  setExcelName,
  getExcelConfigInfo,
  saveExcelConfigInfo,
} from "@/api/signExportSet";
export default {
  name: "CommonExport",
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      tableLoading: false,
      tableNameTemp: {
        id: "",
        name: "",
      },
      modal: {
        set: false,
        detail: false,
      },
      dialogTitle: "",
      defaultProps: {
        children: "excelLeafConfigVos",
        label: "fieldName",
      },
      treeData: [],
      treeObj: {
        excelId: "",
        fieldIdList: [],
      },
      treeSelectedNodes: [],
      submitLoading: false,
      treeLoading: false,
    };
  },
  props: {
    deptCode: {
      type: String,
      default: "",
    },
  },
  created() {
    this.prefixDeptCode = this.deptCode;
  },
  methods: {
    getTableData() {
      this.tableData.records = [];
      this.tableLoading = true;
      getExcelNameList({}, this.prefixDeptCode)
        .then((res) => {
          this.tableData.records = res;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 编辑表格名称
    editTableName(row) {
      this.tableNameTemp.id = row.id;
      this.tableNameTemp.name = row.name;
    },
    // 保存表名称
    saveName(index) {
      setExcelName(this.tableNameTemp, this.deptCode).then((res) => {
        this.$message.success("操作成功");
        this.tableData.records[index].name = this.tableNameTemp.name;
        this.closeAllPopovers();
      });
    },
    // 关闭所有气泡
    closeAllPopovers() {
      // 遍历所有的Popover实例
      const popovers = document.querySelectorAll(".el-popover");
      popovers.forEach((popover) => {
        popover.style.display = "none";
      });
    },
    // 设置弹框
    setShow(row) {
      this.switchModal("set", true);
      this.dialogTitle = row.name + "字段配置";
      this.treeData = [];
      this.treeSelectedNodes = [];
      this.treeObj.excelId = row.id;
      this.treeObj.fieldIdList = [];
      this.getExcelConfigInfo(row.id);
    },
    // 获取导出列字段
    getExcelConfigInfo(id) {
      this.treeLoading = true;
      getExcelConfigInfo({ key: id }, this.prefixDeptCode)
        .then((res) => {
          this.treeData = res;
          for (let i = 0; i < res.length; i++) {
            if (res[i].excelLeafConfigVos) {
              for (let j = 0; j < res[i].excelLeafConfigVos.length; j++) {
                if (res[i].excelLeafConfigVos[j].isConfigured) {
                  let selectedId = res[i].excelLeafConfigVos[j].id;
                  this.treeSelectedNodes.push(selectedId);
                }
              }
            }
          }
        })
        .finally(() => {
          this.treeLoading = false;
        });
    },
    // 设置提交
    handleSetSubmit() {
      let nodeList = this.$refs.tree.getCheckedNodes();
      if (nodeList.length == 0) {
        this.$message.warning("请选择导出字段");
        return false;
      } else {
        this.submitLoading = true;
        this.treeObj.fieldIdList = nodeList.map((item) => item.id);
        saveExcelConfigInfo(this.treeObj, this.prefixDeptCode)
          .then((res) => {
            this.submitLoading = false;
            this.$message.success("操作成功");
            this.switchModal("set", false);
          })
          .catch(() => {
            this.submitLoading = false;
          });
      }
    },
    // 详情弹框
    detailShow(row) {
      this.switchModal("detail", true);
      this.dialogTitle = row.name + "字段配置详情";
      this.treeData = [];
      this.treeObj.excelId = row.id;
      this.treeObj.fieldIdList = [];
      this.getExcelConfigDetail(row.id);
    },
    // 详情获取导出列字段
    getExcelConfigDetail(id) {
      this.treeLoading = true;
      getExcelConfigInfo({ key: id }, this.prefixDeptCode)
        .then((res) => {
          this.treeData = this.analysisNodes(res);
        })
        .finally(() => {
          this.treeLoading = false;
        });
    },
    // 详情只显示已设置的字段
    analysisNodes(arr) {
      let newArr = [];
      arr.forEach((item, index) => {
        newArr[index] = item;
        if (item.excelLeafConfigVos) {
          newArr[index].excelLeafConfigVos = item.excelLeafConfigVos.filter(
            (item) => {
              return item.isConfigured == 1;
            }
          );
        }
      });
      return newArr.filter((item) => {
        return item.excelLeafConfigVos && item.excelLeafConfigVos.length > 0;
      });
    },
  },
};
</script>

<style scoped>
.tree-box {
  height: 550px;
  overflow-y: auto;
}
</style>