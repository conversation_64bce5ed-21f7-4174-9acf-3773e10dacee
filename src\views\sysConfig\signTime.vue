<!-- 报名时间设置 -->
<template>
  <div>
    <el-card>
      <div class="title">报名年龄段设置：</div>
      <el-form
        ref="signAgeForm"
        :rules="rulesSignAge"
        :model="signAgeForm"
        inline
        label-width="120px"
      >
        <el-row>
          <el-col>
            <span class="sub-title">幼儿园报名年龄区间设置：</span>
            <el-form-item prop="minAgeKindergarten" label="最小年龄限制:">
              <el-date-picker
                v-model="signAgeForm.minAgeKindergarten"
                type="date"
                placeholder="选择日期"
                :picker-options="
                  pickerBeginOptions(signAgeForm.maxAgeKindergarten)
                "
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="maxAgeKindergarten" label="最大年龄限制:">
              <el-date-picker
                v-model="signAgeForm.maxAgeKindergarten"
                type="date"
                placeholder="选择日期"
                :picker-options="
                  pickerEndOptions(signAgeForm.minAgeKindergarten)
                "
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          <!-- </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title">小学报名年龄区间设置：</span>
            <el-form-item prop="minAgePrimary" label="最小年龄限制:">
              <el-date-picker
                v-model="signAgeForm.minAgePrimary"
                type="date"
                placeholder="选择日期"
                :picker-options="
                  pickerBeginOptions(signAgeForm.maxAgeKindergarten)
                "
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="maxAgePrimary" label="最大年龄限制:">
              <el-date-picker
                v-model="signAgeForm.maxAgePrimary"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerEndOptions(signAgeForm.minAgePrimary)"
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title">初中报名年龄区间设置：</span>
            <el-form-item prop="minAgeJunior" label="最小年龄限制:">
              <el-date-picker
                v-model="signAgeForm.minAgeJunior"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerBeginOptions(signAgeForm.maxAgeJunior)"
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="maxAgeJunior" label="最大年龄限制:">
              <el-date-picker
                v-model="signAgeForm.maxAgeJunior"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerEndOptions(signAgeForm.minAgeJunior)"
                clearable
                size="small"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14" class="sd-flex-center">
            <el-button type="success" size="small" @click="signAgeFormSubmit"
              >保&emsp;存</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card>
      <div class="title">报名设置：</div>
      <el-form ref="signForm" :rules="rulesSignForm" :model="signForm" inline>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">驳回类别：</span>
            <el-form-item prop="rejectType" label="">
              <el-select
                v-model="signForm.rejectType"
                placeholder="请选择类别"
                multiple
                size="small"
              >
                <el-option
                  v-for="(item, index) in rejectList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">报名志愿数量设置：</span>
            <el-form-item prop="volunteerNumKindergarten" label="幼儿园:">
              <el-input
                v-model="signForm.volunteerNumKindergarten"
                placeholder="输入数量"
                size="small"
                style="width: 100px"
              ></el-input>
              个
            </el-form-item>
            <el-form-item prop="volunteerNumPrimary" label="小学:">
              <el-input
                prop="volunteerNumPrimary"
                v-model="signForm.volunteerNumPrimary"
                placeholder="输入数量"
                size="small"
                style="width: 100px"
              ></el-input>
              个
            </el-form-item>
            <el-form-item prop="volunteerNumJunior" label="初中:">
              <el-input
                v-model="signForm.volunteerNumJunior"
                placeholder="输入数量"
                size="small"
                style="width: 100px"
              ></el-input>
              个
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">是否学校点击报到：</span>
            <el-form-item prop="isClickReport">
              <el-radio-group v-model="signForm.isClickReport">
                <el-radio label="1">开启</el-radio>
                <el-radio label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">是否学校发送录取通知书：</span>
            <el-form-item prop="isSendNotice">
              <el-radio-group v-model="signForm.isSendNotice">
                <el-radio label="1">开启</el-radio>
                <el-radio label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">多胞胎绑定开关设置：</span>
            <el-form-item prop="isMultipleBirthBinding">
              <el-radio-group v-model="signForm.isMultipleBirthBinding">
                <el-radio label="1">开启</el-radio>
                <el-radio label="0">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">报名期间报名信息可修改次数设置：</span>
            <el-form-item prop="signDataEditNum">
              <el-input
                v-model="signForm.signDataEditNum"
                placeholder="输入数量"
                type="number"
                :min="0"
                size="small"
                style="width: 110px"
              ></el-input>
              次
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14">
            <span class="sub-title1">一个微信可以报名数量设置：</span>
            <el-form-item prop="oneWeixinSignNum">
              <el-input
                v-model="signForm.oneWeixinSignNum"
                placeholder="输入数量"
                type="number"
                :min="0"
                size="small"
                style="width: 110px"
              ></el-input>
              次
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="14" class="sd-flex-center">
            <el-button type="success" size="small" @click="signFormSubmit"
              >保&emsp;存</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      signAgeForm: {
        minAgeKindergarten: "",
        maxAgeKindergarten: "",
        minAgePrimary: "",
        maxAgePrimary: "",
        minAgeJunior: "",
        maxAgeJunior: "",
      },
      rulesSignAge: {
        minAgeKindergarten: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        maxAgeKindergarten: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        minAgePrimary: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        maxAgePrimary: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        minAgeJunior: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
        maxAgeJunior: [
          { required: true, message: "请选择日期", trigger: "blur" },
        ],
      },
      signForm: {
        rejectType: [],
        volunteerNumKindergarten: "",
        volunteerNumPrimary: "",
        volunteerNumJunior: "",
        isClickReport: "",
        isSendNotice: "",
        isMultipleBirthBinding: "",
        oneWeixinSignNum: "",
      },
      rulesSignForm: {},
      rejectList: [
        {
          label: "驳回-修改信息",
          value: "1",
        },
        {
          label: "驳回-重新报名",
          value: "2",
        },
        {
          label: "驳回-不可重报",
          value: "3",
        },
      ],
    };
  },
  created() {},
  methods: {
    pickerBeginOptions(endTime) {
      return {
        disabledDate(time) {
          if (endTime) {
            return time.getTime() > new Date(endTime).getTime();
          }
        },
      };
    },
    pickerEndOptions(beginTime) {
      return {
        disabledDate(time) {
          if (beginTime) {
            return time.getTime() < new Date(beginTime).getTime();
          }
        },
      };
    },
    signAgeFormSubmit() {},
    signFormSubmit() {},
  },
};
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
  .title {
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 20px;
  }
  .sub-title {
    font-size: 14px;
    font-weight: bold;
    padding-top: 10px;
    display: inline-block;
    width: 260px;
    text-align: right;
  }
  .sub-title1 {
    width: 300px;
    font-size: 14px;
    font-weight: bold;
    padding-top: 10px;
    display: inline-block;
    width: 260px;
    text-align: right;
  }
  .el-input,
  .el-select,
  .el-date-picker {
    width: 200px;
  }
}
</style>