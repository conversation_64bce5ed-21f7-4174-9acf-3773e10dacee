<template>
  <el-form
    :model="form"
    :rules="rules"
    ref="form"
    label-width="auto"
    @keydown.enter="save"
  >
    <el-form-item label="模板文件">
      <el-button
        type="info"
        @click="downloadTemplateFile"
        icon="el-icon-download"
        size="small"
        >下载模板</el-button
      >
    </el-form-item>
    <el-form-item label="选择文件" prop="file">
      <el-upload
        ref="upload"
        accept=".xlsx,.xls"
        :action="importUrl"
        :on-success="importSuccess"
        :on-error="importError"
        :file-list="fileList"
        :auto-upload="false"
        :limit="1"
        :headers="headers"
        :on-remove="importRemove"
        :on-exceed="importExceed"
        :before-upload="importBeforeUpload"
        :on-change="importChange"
      >
        <el-button size="small" type="primary" icon="el-icon-folder-opened"
          >选取文件</el-button
        >
        <span :class="{ 'sd-warning-text': showErrorTip }" slot="tip" m-l-4
          >文件最大不超过10M</span
        >
      </el-upload>
      <div
        pos-absolute
        pos-top="20"
        class="sd-error-text"
        v-for="(item, index) in errorMessages"
        :key="index"
      >
        {{ index + 1 }}、{{ item.message }}
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import FormMixin from "@/mixins/FormMixin";
import { mapGetters } from "vuex";

export default {
  name: "ImportUser",
  mixins: [FormMixin],
  data() {
    return {
      importUrl: "",
      fileList: [],
      showErrorTip: false,
      errorMessages: [],
    };
  },
  computed: {
    ...mapGetters(["token"]),
    headers() {
      return {
        Authorization: `${this.token}`,
      };
    },
  },
  methods: {
    importChange(file, fileList) {
      console.log("this.importChange", file, fileList);
      if (file && file.size > 1024 * 1024 * 10) {
        this.showErrorTip = true;
        this.errorMessages = [{ index: 0, message: "文件大小不能超过10M" }];
      } else if (file.status !== "fail") {
        this.errorMessages = [];
      }
    },
    importRemove() {
      this.errorMessages = [];
    },
    importSuccess(response, file, fileList) {
      console.log(response, file, fileList);
    },
    importError(err) {
      this.$emit("save-complete", 0);
      this.errorMessages = [{ index: 0, message: err.message }];
    },
    importExceed() {
      this.$message.error("最多只能上传一个文件");
    },
    importBeforeUpload(file) {
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error("文件大小不能超过10M");
        this.showErrorTip = true;
        this.$emit("save-complete", 0);
      }
    },
    downloadTemplateFile() {},
    saveSubmit() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang='scss' scoped></style>