<template>
  <div>
    <el-row>
      <el-col :span="16" :offset="3">
        <el-form
          :model="form"
          ref="form"
          :rules="rules"
          label-width="90px"
          label-position="right"
        >
          <el-form-item label="账号">
            <span>{{ username }}</span>
          </el-form-item>
          <el-form-item label="选择学段" prop="schoolType">
            <el-select
              v-model="form.schoolType"
              style="width: 100%"
              @change="schoolTypeChange"
            >
              <el-option
                v-for="item in schoolTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择学校" prop="schoolIds">
            <el-select v-model="form.schoolIds" multiple style="width: 100%">
              <el-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.deptName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import FormMixin from "@/mixins/FormMixin";
import { getUserAuditDetail, auditSchool } from "@/api/user";
import { getDepts } from "@/api/common"
import { schoolTypeOptions } from "@/utils/common";
export default {
  name: "ImportUser",
  mixins: [FormMixin],
  data() {
    return {
      form: {
        userId: this.data.id,
        schoolType: "",
        schoolIds: [],
      },
      schoolTypeOptions: schoolTypeOptions,
      schoolList: [],
      deptId: this.data.deptId,
      username: this.data.username,
      rules: {
        schoolType: [
          { required: true, message: "请选择学段", trigger: "change" },
        ],
        schoolIds: [
          { required: true, message: "请选择学校", trigger: "change" },
        ],
      },
    };
  },
  created() {
    // console.log(this.data);
    this.getUserAuditDetail();
  },
  methods: {
    // 查询权限详情
    getUserAuditDetail() {
      getUserAuditDetail({ key: this.form.userId }).then((res) => {
        this.form.schoolType = res.schoolType;
        this.form.schoolIds = res.schoolIds;
        this.getDepts();
      });
    },
    // 选择学段
    schoolTypeChange() {
      this.form.schoolIds = [];
      this.getDepts();
    },
    // 根据学段查询学校
    getDepts() {
      if (this.form.schoolType) {
        let params = {
          level: 3,
          period: this.form.schoolType,
          parentId: this.deptId,
        };
        getDepts(params).then((res) => {
          this.schoolList = res;
        });
      }
    },
    // 提交
    saveSubmit() {
      auditSchool(this.form).then((res) => {
        this.$emit("save-complete", 1);
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.el-form {
  padding-bottom: 100px;
  min-height: 240px;
}
</style>