<template>
  <div>
    <div class="sd-option-container">
      <div class="sd-options">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="showAddForm"
          size="small"
          >添加</el-button
        >
        <el-button
            type="primary"
            icon="el-icon-plus"
            @click="cityAdds"
            size="small"
        >添加市级账号</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="small"
          @click="exportList"
          :loading="exportLoading"
          v-if="role == 'SUPER_ADMIN'"
          >导出</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          v-if="role == 'SUPER_ADMIN'"
          >批量导入</el-button
        >
      </div>
      <div class="sd-search">
        <el-select
          v-model="search.ancestors"
          size="small"
          placeholder="所属区县"
          clearable
        >
          <el-option
            v-for="item in deptOptions"
            :key="item.id"
            :value="item.id"
            :label="item.deptName"
          ></el-option>
        </el-select>
        <el-input
          v-model.trim="search.keywords"
          placeholder="请输入关键字"
          size="small"
          clearable
        ></el-input>
        <el-button
          type="primary"
          class="sd-btn-search"
          icon="el-icon-search"
          @click="searchSubmit"
          size="small"
        />
      </div>
    </div>
    <el-table :data="tableData.records" border stripe v-loading="tableLoading">
      <el-table-column
        type="index"
        :index="getTableIndex"
        label="序号"
        width="60"
        align="center"
      />
      <el-table-column
        prop="roleName"
        label="角色"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="deptName"
        label="所属部门"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="username"
        label="账号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="defaultPasswordView"
        label="默认密码"
        align="center"
      >
        <template slot-scope="{ row }">
          <span>{{ row.defaultPasswordView }}</span>
          <el-link
            @click="changePasswordView(row)"
            :underline="false"
            :icon="row.showPasswordView ? 'el-icon-view' : ''"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="nickname"
        label="姓名"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="wxname"
        label="微信绑定昵称"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column prop="" label="权限划分" align="center">
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="showPermitForm(row)"
            v-if="row.roleName == '区县教育局审核员'"
            >分配权限</el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="150" align="center">
        <template slot-scope="{ row }">
          {{ row.status == 1 ? "启用" : "禁用" }}
        </template>
      </el-table-column>
      <el-table-column prop="id" label="操作" width="300" align="center">
        <template slot-scope="{ row }">
          <el-link
              v-if="row.deptId!=1"
            type="warning"
            icon="el-icon-edit"
            @click="showEditForm(row)"
            :underline="false"
            style="margin-right: 10px"
            >编辑
          </el-link>
          <el-link
            type="primary"
            icon="el-icon-key"
            @click="resetPassword(row)"
            :underline="false"
            style="margin-right: 10px"
            >重置密码
          </el-link>
          <el-link
            v-if="row.status == 0"
            type="success"
            icon="el-icon-check"
            @click="changeStatus(row)"
            :underline="false"
            style="margin-right: 10px"
            >启用
          </el-link>
          <el-link
            v-if="row.status == 1"
            type="info"
            icon="el-icon-close"
            @click="changeStatus(row)"
            :underline="false"
            style="margin-right: 10px"
            >禁用
          </el-link>
          <el-link
            type="danger"
            icon="el-icon-delete"
            @click="delUser(row)"
            :underline="false"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="sd-pagination-container">
      <el-pagination
        background
        layout="total, prev, pager, next,sizes"
        :total="Number(tableData.total || 0)"
        :current-page.sync="search.pageNumber"
        :page-size.sync="search.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :title="dialogFormComponent.title()"
      width="600px"
      append-to-body
      :visible.sync="dialogFormComponent.dialogFormVisible"
      :close-on-click-modal="false"
      center
      @close="dialogFormComponent.close()"
    >
      <div>
        <component
          :ref="dialogFormComponent.name"
          :is="dialogFormComponent.name"
          :data="dialogFormComponent.data"
          :mode="dialogFormComponent.mode"
          @save-complete="dialogFormComponent.saveComplete"
        />
      </div>
      <div class="flex-center">
        <el-button
          type="default"
          @click="dialogFormComponent.close()"
          size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="dialogFormComponent.save()"
          :loading="dialogFormComponent.saveLoading"
          size="small"
          >保 存</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
        title="添加市级账号"
        :visible.sync="cityShow"
        width="600px"
        append-to-body
        :close-on-click-modal="false"
        center
    >
      <el-form :model="cityForm" :rules="rules">
        <el-form-item label="账号" prop="username">
          <el-input
              v-model.trim="cityForm.username"
              placeholder="请输入用户名"
              clearable
              maxlength="32"
              show-word-limit
              size="small"
              style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="nickname">
          <el-input
              v-model.trim="cityForm.nickname"
              placeholder="请输入真实姓名"
              clearable
              maxlength="32"
              show-word-limit
              size="small"
              style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="电话" prop="mobile">
          <el-input
              v-model.trim="cityForm.mobile"
              placeholder="请输入手机号码"
              clearable
              maxlength="11"
              show-word-limit
              size="small"
              style="width: 215px"
          />
        </el-form-item>
        <el-form-item label="默认密码">
          <div style="color: red; line-height: 20px; padding-top: 9px">
            教育局总账号密码生成规则：随机生成数字+字母+特殊符号8—12位组合密码
          </div>
        </el-form-item>
      </el-form>
      <div
          slot="footer"
          class="dialog-footer"
      >
        <el-button @click="cityShow = false">取 消</el-button>
        <el-button
            type="primary"
            @click="cityAdd"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          :loading="importLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import AddForm from "@/views/sysConfig/user/userForm.vue";
import EditForm from "@/views/sysConfig/user/userForm.vue";
// import ImportForm from "@/views/sysConfig/user/importForm.vue";
import PermitForm from "@/views/sysConfig/user/permitForm.vue";
import {
  getUserList,
  resetPwd,
  enableUser,
  disableUser,
  delUser,
  importUser, create
} from "@/api/user";
import { getDepts } from "@/api/common";
import {mobileValidator} from "@/utils/validator";

export default {
  name: "UserList",
  mixins: [TableMixin,  ModalMixin],
  components: { AddForm, EditForm, PermitForm },
  data() {
    return {
      role: this.$store.getters.role,
      cityShow:false,
      cityForm:{
        deptId: null,
        roleId: null,
        username: null,
        nickname: null,
        mobile: null,
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        nickname: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { validator: mobileValidator, trigger: "blur" },
        ]
      },
      search: {
        pageNumber: 1,
        pageSize: 10,
        keywords: null,
        ancestors: "",
      },
      deptOptions: [],
      exportLoading: false,
      dialogTitle: "批量导入",
      modal: {
        import: false,
      },
      fileList: [],
      errorMessages: [],
      importLoading: false
    };
  },
  async created() {
    if (this.role == "SUPER_ADMIN") {
      this.deptOptions = await getDepts({ level: 2 });
    }
  },
  methods: {
    cityAdds(){
      this.cityShow=true
      this.cityForm.roleId=1
      this.cityForm.deptId=1
    },
    cityAdd(){
      create(this.cityForm).then(() => {
        this.cityShow=false
        this.cityForm={}
        this.getTableData()
       this.$message.success('添加成功')
      })

    },
    async getTableData() {
      this.tableLoading = true;
      const data = await getUserList(this.search);
      for (let i = 0; i < data.records.length; i++) {
        const item = data.records[i];
        item.defaultPasswordView = "********";
        item.showPasswordView = true;
      }
      this.tableData = data;
      this.tableLoading = false;
    },
    changePasswordView(row) {
      if (row.showPasswordView) {
        row.defaultPasswordView = row.defaultPassword;
      } else {
        row.defaultPasswordView = "******";
      }
      row.showPasswordView = !row.showPasswordView;
    },
    delUser(row) {
      this.$confirm(`确定删除【${row.username}】吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        delUser({ key: row.id }).then(() => this.getTableData());
      });
    },
    changeStatus(row) {
      this.$confirm(
        `确定${row.status == 1 ? "禁用" : "启用"}【${row.username}】吗？`,
        "提示",
        { type: "warning" }
      ).then(() => {
        this.$message.success("操作成功");
        console.log(row.status);
        if (row.status == "1") {
          disableUser({ key: row.id }).then(() => this.getTableData());
        } else {
          enableUser({ key: row.id }).then(() => this.getTableData());
        }
      });
    },
    resetPassword(row) {
      this.$confirm(`确定重置【${row.username}】的密码吗？`, "提示", {
        type: "warning",
      }).then(() => {
        this.$message.success("操作成功");
        resetPwd({ key: row.id }).then(() => this.getTableData());
      });
    },
    showPermitForm(row) {
      this.dialogFormComponent.name = "PermitForm";
      this.dialogFormComponent.mode = "Permit";
      this.dialogFormComponent.show("PermitForm", row);
    },
    // 导出
    exportList() {
      this.exportLoading = true;
      let params = this.search;
      this.$download(
          '/user-api/center/user/exportUserList',
          params,
          "xls",
          "导出账号列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
    // 批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      this.$download(
        "/user-api/center/user/downTemplate",
        {},
        "xls",
        "账号批量导入模版.xls"
      ).then((res) => {
        this.$message.success("下载成功")
      });
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      this.importLoading = true;
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      importUser(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      }).finally(err => {
        this.importLoading = false;
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
