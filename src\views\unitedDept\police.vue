<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="exportList"
          >导出公安信息</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="openImport"
          >导入公安信息</el-button
        >
      </div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="enrollId">
            <el-input
              size="small"
              v-model.trim="search.enrollId"
              placeholder="报名ID"
              style="width: 185px"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="keywords">
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="姓名或身份证"
              style="width: 185px"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="createdTime">
            <el-date-picker
              size="small"
              v-model="search.createdTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="报名开始日期"
              style="width: 185px"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="endTime">
            <el-date-picker
              size="small"
              v-model="search.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="报名结束日期"
              style="width: 185px"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="publicSecurityAndHouseReviewStatus">
            <el-select
              size="small"
              v-model="search.publicSecurityAndHouseReviewStatus"
              placeholder="公安户口审核状态"
              style="width: 185px"
              clearable
            >
              <el-option label="待审核" :value="1"></el-option>
              <el-option label="通过" :value="2"></el-option>
              <el-option label="不通过" :value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe>
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
      ></el-table-column>
      <el-table-column
        align="center"
        label="报名ID"
        width="150"
        prop="enrollId"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学生姓名"
        prop="studentName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="身份证号"
        prop="studentIdCardNumber"
        width="180"
      ></el-table-column>
      <el-table-column
        align="center"
        label="类别"
        prop="type"
        width="300"
      ></el-table-column>
      <el-table-column
        align="center"
        label="户号"
        prop="accountNumber"
      ></el-table-column>
      <el-table-column
        align="center"
        label="居住证编号"
        prop="certificateCode"
      ></el-table-column>
      <el-table-column
        align="center"
        label="户主"
        prop="householdName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="与该生关系"
        prop="relationOfStudent"
      >
        <template slot-scope="scope">
          {{ relationCn(scope.row.relationOfStudent) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="公安户口审核"
        prop="publicSecurityReviewStatus"
      ></el-table-column>
      <el-table-column align="center" label="操作" width="300px">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-view"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="detail(row)"
            >详情</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '通过'"
            @click="pass(row)"
            >通过</el-link
          >
          <el-link
            icon="el-icon-close"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            :disabled="row.publicSecurityReviewStatus == '不通过'"
            @click="reject(row)"
            >不通过</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="modal.stuDetail"
      :close-on-click-modal="false"
      title="学生报名详情"
      center
      width="1240px"
      @close="stuDetailClose"
    >
      <enroll-detail
        :stu-detail="curStuDetail"
        :key="curStuDetail.studentBaseId"
      ></enroll-detail>
      <el-button size="small" type="info" @click="stuDetailClose"
        >关闭</el-button
      >
    </el-dialog>
    <!-- 导入 -->
    <el-dialog
      :visible.sync="modal.import"
      center
      title="导入信息"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-upload
        action="#"
        :limit="upload.limit"
        :auto-upload="upload.auto"
        :file-list="upload.list"
        :on-change="changeFile"
        :on-remove="removeFile"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>
      <div slot="footer">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :disabled="upload.list.length == 0"
          @click="confirmUpload"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 不通过 -->
    <el-dialog
      title="不通过"
      :visible.sync="modal.reject"
      center
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="rejectForm"
        ref="rejectForm"
        :rules="rejectRules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item prop="rejectStatus" label="不通过原因：">
          <el-radio-group v-model="rejectForm.rejectStatus">
            <el-radio :label="1">信息不符</el-radio>
            <el-radio :label="2">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :prop="rejectForm.rejectStatus == 2 ? 'cause' : 'empty'"
          label="原因："
        >
          <el-input
            type="textarea"
            :rows="5"
            v-model.trim="rejectForm.cause"
            placeholder="请输入不通过原因，最多200个字符"
            style="width: 400px"
            maxlength="200"
            show-word-limit
            :disabled="rejectForm.rejectStatus != 2"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="flex-center sd-m-t-15">
        <el-button size="small" @click="switchModal('reject', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmReject"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import { policeList, policeIm } from "@/api/adInfo";
import {
  passSecurityAndHouseAudit,
  notPassSecurityAndHouseAudit,
} from "@/api/enrollment";
import { relationList } from "@/utils/dictionary";
import { pref } from "@/utils/common";
import EnrollDetail from "@/components/EnrollDetail";
export default {
  mixins: [TableMixin, ModalMixin],
  components: { EnrollDetail },
  data() {
    return {
      prefixDeptCode: this.$store.getters.deptCode,
      // 当前学生报名详情
      curStuDetail: {},
      role: this.$store.getters.role,
      search: {
        enrollId: "",
        keywords: "",
        createdTime: "",
        endTime: "",
        publicSecurityAndHouseReviewStatus: "",
        type: 0,
        publicSecurityReviewType: 1, // 公安审核类型（1：户口 2：居住证）
      },
      modal: {
        import: false,
        reject: false,
        stuDetail: false,
      },
      upload: {
        limit: 1,
        auto: false,
        list: [],
      },
      rejectForm: {
        type: 0,
        studentId: "",
        rejectStatus: "",
        cause: "",
      },
      rejectRules: {
        rejectStatus: [
          {
            required: true,
            message: "请选择驳回原因",
            trigger: "change",
          },
        ],
        cause: [
          {
            required: true,
            message: "请输入驳回原因",
            trigger: "blur",
          },
          {
            max: 200,
            message: "最多200个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    // 详情 - 关闭
    stuDetailClose() {
      this.switchModal("stuDetail", false);
      this.index = 0;
    },
    // 详情
    detail(row, index) {
      this.curStuDetail = row;
      this.index = index;
      this.modal.stuDetail = true;
      // this.getAuditStatus();
    },
    // 关系
    relationCn(val) {
      return relationList.find((v) => v.id == val)?.val || "";
    },
    // 导出
    exportList() {
      let params = { ...this.search };
      // if (this.prefixDeptCode != "130427") {
      this.$download(
        `${pref}${this.prefixDeptCode}/biz/excelConfig/exportExcelPublicHouse`,
        params,
        "xls",
        "户口信息列表.xls"
      ).then((res) => {
        this.$message.success("下载成功");
      });
      // } else {
      //   // 磁县130427
      //   this.$download(
      //     `${pref}${this.prefixDeptCode}/biz/security/cxExportSecurity`,
      //     params,
      //     "xls",
      //     "户口信息列表.xls"
      //   ).then((res) => {
      //     this.$message.success("下载成功");
      //   });
      // }
    },
    // 打开导入
    openImport() {
      this.switchModal("import", true);
      this.removeFile();
    },
    // 上传
    changeFile(file, fileList) {
      this.upload.list = [file.raw];
    },
    // 删除上传
    removeFile() {
      this.upload.list = [];
    },
    // 列表
    getTableData() {
      policeList(this.search, this.prefixDeptCode).then(
        (data) => (this.tableData = data)
      );
    },
    // 上传
    confirmUpload() {
      let fd = new FormData();
      fd.append("file", this.upload.list[0]);
      fd.append("publicSecurityReviewType", 1);
      policeIm(fd, this.prefixDeptCode).then((res) => {
        this.$message.success("操作成功");
        this.switchModal("import", false);
        this.getTableData();
      });
    },
    // 通过
    pass(row) {
      this.$confirm("确认该学生信息无误，审核通过？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        passSecurityAndHouseAudit(
          { studentId: row.studentBaseId, type: 0 },
          this.prefixDeptCode
        ).then(() => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 驳回
    reject(row) {
      this.switchModal("reject", true);
      this.$nextTick(() => {
        this.$refs.rejectForm.resetFields();
        this.rejectForm.studentId = row.studentBaseId;
      });
    },
    // 驳回确定
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          let params = {
            studentId: this.rejectForm.studentId,
            type: this.rejectForm.type,
            cause:
              this.rejectForm.rejectStatus == 1
                ? "信息不符"
                : this.rejectForm.cause,
          };
          notPassSecurityAndHouseAudit(params, this.prefixDeptCode).then(
            (res) => {
              this.$message.success("操作成功");
              this.switchModal("reject", false);
              this.getTableData();
            }
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
