const { defineConfig } = require('@vue/cli-service')

const UnoCSS = require('@unocss/webpack').default
const CompressionPlugin = require("compression-webpack-plugin")
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

const ts = (new Date).getTime()

module.exports = defineConfig({
  publicPath: "/admin",
  outputDir: "admin",
  assetsDir: "static",
  configureWebpack: {
    plugins: [
      UnoCSS(),
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|less)$/, // 匹配文件名
        threshold: 10240, // 对超过10k的数据压缩
        minRatio: 0.8,
      }),
			new MiniCssExtractPlugin({
				filename: `css/[name].${ ts }.css`,
				chunkFilename: `css/chunk.[id].${ ts }.css`
			})
    ],
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    },
  },
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    open: true,
    port: 81,
    hot: true,
    client: {
      overlay: false
    },
    allowedHosts: "all",
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:38080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
})
